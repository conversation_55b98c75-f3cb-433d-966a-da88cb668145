# -*- coding:UTF-8 -*-
# @FileName  :dp_d5.py
# @Time      :2024/6/4 15:34
# <AUTHOR>
import base64
import random

from DrissionPage import ChromiumPage, ChromiumOptions, WebPage
from PIL import Image


# 移动的轨迹
def trajectory(distance):
    # d=vt+1/2at**2==>位移=初速度*时间+1/2加速度*时间的平方
    # v1=v+at
    # 思路:先加速,然后加速
    # 移动轨迹
    distance -= 6
    track = []
    # 当前移动的距离
    current = 0
    # 移动到某个地方开始减速
    mid = distance * 3 / 4
    # 间隔时间(加速时间)
    t = 2
    # 初速度
    v = 10
    pass
    while True:
        if (current < mid):
            a = random.randint(2, 3)
        else:
            a = random.randint(4, 5)
        v0 = v
        v = v0 + a * t
        move = v0 * t + 1 / 2 * a * t ** 2
        current += move
        track.append(round(move))
        if current > distance:
            break
    return track


def save_base64_image(base64_string, output_path):
    """
    保存Base64编码的图片到指定路径。

    :param base64_string: Base64编码的图像数据字符串。
    :param output_path: 图片保存的路径，包括文件名和扩展名。
    """
    # 移除"data:image/jpeg;base64,"前缀，如果存在的话
    if base64_string.startswith("data:image/"):
        base64_string = base64_string.split(",")[-1]

    # 解码Base64字符串
    image_data = base64.b64decode(base64_string)

    # 保存为图片文件
    with open(output_path, 'wb') as f:
        f.write(image_data)


def merge_images(bg_path, ms_path, result_path):
    # 打开图片
    bg_image = Image.open(bg_path)
    ms_image = Image.open(ms_path)

    # 计算居中放置的位置，这里不改变ms_image的尺寸
    bg_width, bg_height = bg_image.size
    ms_width, ms_height = ms_image.size

    # 创建一个新的图像用于合并，确保新图像足够大以容纳两者
    combined_height = bg_height + ms_height  # 假设ms图片放在bg图片上方
    combined_image = Image.new('RGB', (bg_width, combined_height), color='white')  # 或者使用bg_image的颜色作为背景色
    # 先ms图片粘贴到顶部
    combined_image.paste(ms_image, (0, 0))
    # 再粘贴背景图片
    combined_image.paste(bg_image, (0, ms_height))
    # 保存结果图片为JPEG
    combined_image.save(result_path, format='JPEG')
    # # 显示结果图片（可选）
    # combined_image.show()


li = trajectory(300)

page = WebPage()
url = 'https://s.1688.com//youyuan/index.htm/_____tmd_____/punish?x5secdata=xckhd7A0BkfS5vWKP4OhCSqKkpIgkJbkJEgr8xD7vilcS1U%2fU8xKAq9mTgDFvXlLRadJKf09gMuvXgqdSIl7kj6jOhobizbHX3AiQIbSVQFAf4k7yHiYwy4xDOAsbRkQO%2bmhZ%2bPjKBJJ1zNIZadOiytCHG8sTcwan6HDDTVvmPwscIcgj96%2f1BibMN4OurO4uP0JQSw5cJQu1Ug%2f4skgbrrIKSEOExVNmDiC%2fE%2biNbmRUHA%2bqMs2nRxasRSFCch2XkQ1tQsmg%2fAHqy1ZHOtupQspco%2fvA%2fgDYBVXOc22Vm39rVJB1RTV01%2bvPo7EPM3yik6k%2fhoFvFxhZz1q0tyXLsIR6UbCaEoFwqtce%2fQvevF4AW4rBLlBOoTXwCIVdw9whSUE7EZsYPyjYa2SXBhof1yQ%3d%3d__bx__s.1688.com%2fyouyuan%2findex.htm&x5step=1'
# url = 'https://s.1688.com//youyuan/index.htm/_____tmd_____/punish?x5secdata=xcpHvzjaEJJakjSLjC5cIGzgN3VpyIuk3dvnrc5aelO7cvlbO0x3BsAsgl9pos2roLFVp1umNzmmCLdEzSzLPABRArYVAHvbadvYWLiFQEBihBL2tOBXBBBzVq2ukOZvroqPAfpHJElgWCASNOfM1G8BTwUCYbct1gf1TXe0msNtGTL45N%2fOK4w%2bWFztqgJyG6H%2b4yw4QgXfwziDflZxZvzShWXNJnSJb4Qu0HJFByO3%2flIKfDiq8MRqZzH2WGNgZt3SU9uk1ibVEceINfcAnStN0zogZhS9APW%2f3VuGzOHl2%2b8dbr8jAonHfnLRd6qCUwIa6B4JNxw1VE%2bGCKpqGtRDxbZhO8xcbZyfMzKEDLjss%3d__bx__s.1688.com%2fyouyuan%2findex.htm&x5step=1'

page.listen.start('1688.com/youyuan/index.htm/_____tmd_____/newslidecaptcha')
page.get(url)
page.wait.load_start()

print(page.title)
# e = page('c=#nc_1_n1z')
# print(li)
# for i in li:
#     # e.drag(i, random.randint(-3, 3))
#     page.actions.hold(e).move(i, random.randint(-3, 3))
#     # page.actions.hold(e).move(i, random.randint(-4, 4),.2).move(-random.randint(1, 3), random.randint(-4, 4),.2)

# for packet in page.listen.steps():
#     if packet.response.body.get('success', ''):
#         data = packet.response.body.get('data')
#         # 图片文件路径
#         bg_path = '1bg.jpg'
#         ms_path = '1ms.jpg'
#         result_path = '1result.jpg'
#         save_base64_image(data['imageData'], bg_path)
#         save_base64_image(data['ques'], ms_path)
#         merge_images(bg_path, ms_path, result_path)
res = page.listen.wait()  # 等待并获取一个数据包
if res.response.body.get('success', ''):
    data = res.response.body.get('data')
    # 图片文件路径
    bg_path = '1bg.jpg'
    ms_path = '1ms.jpg'
    result_path = '1result.jpg'
    save_base64_image(data['imageData'], bg_path)
    save_base64_image(data['ques'], ms_path)
    merge_images(bg_path, ms_path, result_path)
