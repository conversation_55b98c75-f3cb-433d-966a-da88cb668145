# -*- coding:UTF-8 -*-
# @FileName  :dp_d5.py
# @Time      :2024/6/4 15:34
# <AUTHOR>
import time

from DrissionPage import ChromiumPage, ChromiumOptions

# co = ChromiumOptions().set_local_port(9222).use_system_user_path()
# page = ChromiumPage(addr_or_opts=co)
page = ChromiumPage()
page.set.window.show()
page.set.window.max()
page.get('https://www.geetest.com/adaptive-captcha-demo')
print(page.title)
# page('@aria-label=点击按钮开始验证').click()

page('c=tab-item tab-item-1').click()
page('@aria-label=点击按钮开始验证').click()
page('c:geetest_btn').drag(300, 2, 3)
