# -*- coding:UTF-8 -*-
# @FileName  :amazon_delivery_spider.py
# @Time      :2024/12/20
# <AUTHOR>
import time
import traceback
from utils_mrc.pub_fun import *
from work.自动化.亚马逊.Amazon import AmazonSK
from work.自动化.亚马逊.AmazonDP import AmazonDP
from utils_mrc.MysqlHelper import logging
from utils_mrc.FeiShuAPI import fsmsg


class AmazonDeliverySpider:
    def __init__(self, proxy_type=1, port=None):
        """
        初始化亚马逊到货爬虫
        :param proxy_type: 代理类型 0=不使用代理, 1=使用代理池1, 2=使用代理池2
        :param port: 浏览器端口，None时自动分配
        """
        self.proxy_type = proxy_type
        self.port = port
        
        # 根据需求选择不同的基类
        # 如果需要Session方式（轻量级）：
        self.amazon = AmazonSK(port=port, proxy_type=proxy_type)
        
        # 如果需要浏览器方式（功能完整）：
        # self.amazon_dp = AmazonDP(local_port=port, proxy_type=proxy_type)
        
        logging(f'亚马逊到货爬虫初始化完成，代理类型：{proxy_type}，端口：{port or "自动分配"}')

    def check_delivery_status(self, asin, site='com'):
        """
        检查商品到货状态
        :param asin: 商品ASIN
        :param site: 站点 (com, de, uk, etc.)
        :return: 到货信息
        """
        try:
            # 构建商品URL
            url = f'https://www.amazon.{site}/dp/{asin}'
            
            # 访问页面
            self.amazon.goto(url)
            
            # 检查页面状态
            if not self.amazon.check_page():
                raise Exception(f'页面访问失败：{self.amazon.cur_info.get("result", "未知错误")}')
            
            # 获取页面HTML
            html = self.amazon.get_html()
            
            # 解析到货信息
            delivery_info = self.parse_delivery_info(html, asin)
            
            logging(f'ASIN {asin} 到货信息获取成功')
            return delivery_info
            
        except Exception as e:
            error_msg = f'检查ASIN {asin} 到货状态失败：{str(e)}'
            logging(error_msg)
            return {'asin': asin, 'error': error_msg, 'status': 'failed'}

    def parse_delivery_info(self, html, asin):
        """
        解析页面中的到货信息
        :param html: 页面HTML
        :param asin: 商品ASIN
        :return: 解析后的到货信息
        """
        delivery_info = {
            'asin': asin,
            'status': 'unknown',
            'delivery_date': '',
            'availability': '',
            'stock_status': '',
            'parse_time': now_int()
        }
        
        try:
            # 这里添加具体的解析逻辑
            # 示例解析代码（需要根据实际页面结构调整）
            
            # 检查库存状态
            if 'Currently unavailable' in html or '目前无法获得' in html:
                delivery_info['stock_status'] = 'out_of_stock'
                delivery_info['status'] = 'unavailable'
            elif 'In stock' in html or '有现货' in html:
                delivery_info['stock_status'] = 'in_stock'
                delivery_info['status'] = 'available'
            
            # 提取到货日期（需要根据实际页面结构调整选择器）
            # delivery_date_element = self.amazon.page.ele('选择器')
            # if delivery_date_element:
            #     delivery_info['delivery_date'] = delivery_date_element.text
            
            logging(f'ASIN {asin} 解析完成：{delivery_info["status"]}')
            
        except Exception as e:
            logging(f'解析ASIN {asin} 到货信息时出错：{str(e)}')
            delivery_info['error'] = str(e)
            
        return delivery_info

    def batch_check_delivery(self, asin_list, site='com'):
        """
        批量检查商品到货状态
        :param asin_list: ASIN列表
        :param site: 站点
        :return: 检查结果列表
        """
        results = []
        
        for i, asin in enumerate(asin_list):
            try:
                logging(f'正在检查第 {i+1}/{len(asin_list)} 个商品：{asin}')
                
                result = self.check_delivery_status(asin, site)
                results.append(result)
                
                # 添加延迟避免被反爬
                time.sleep(2)
                
            except Exception as e:
                error_result = {
                    'asin': asin,
                    'error': str(e),
                    'status': 'failed',
                    'parse_time': now_int()
                }
                results.append(error_result)
                logging(f'检查ASIN {asin} 时发生异常：{str(e)}')
        
        return results

    def save_results(self, results):
        """
        保存检查结果到数据库
        :param results: 检查结果列表
        """
        try:
            # 这里添加保存到数据库的逻辑
            # 示例代码：
            # for result in results:
            #     MS.insert("INSERT INTO delivery_check_results (...) VALUES (...)", result)
            
            logging(f'成功保存 {len(results)} 条到货检查结果')
            
        except Exception as e:
            logging(f'保存结果时出错：{str(e)}')

    def close(self):
        """
        关闭爬虫，释放资源
        """
        try:
            if hasattr(self.amazon, 'page') and self.amazon.page:
                self.amazon.page.quit()
            logging('亚马逊到货爬虫已关闭')
        except Exception as e:
            logging(f'关闭爬虫时出错：{str(e)}')


def main():
    """
    主函数
    """
    spider = None
    try:
        # 初始化爬虫（根据环境选择代理类型）
        proxy_type = 1 if not is_test_environment() else 0
        spider = AmazonDeliverySpider(proxy_type=proxy_type)
        
        # 示例：检查单个ASIN
        # result = spider.check_delivery_status('B08N5WRWNW', 'com')
        # print(result)
        
        # 示例：批量检查ASIN列表
        asin_list = ['B08N5WRWNW', 'B07XJ8C8F5']  # 替换为实际的ASIN列表
        results = spider.batch_check_delivery(asin_list, 'com')
        
        # 保存结果
        spider.save_results(results)
        
        logging('到货检查任务完成')
        
    except Exception as e:
        error_msg = f'主程序执行异常：{traceback.format_exc()}'
        logging(error_msg)
        fsmsg.send('亚马逊到货爬虫', '程序异常', error_msg)
        
    finally:
        if spider:
            spider.close()


if __name__ == '__main__':
    logging(f'开始运行亚马逊到货爬虫 {get_cur_run_file()}...')
    
    while True:
        try:
            main()
            break  # 单次运行完成后退出，如需循环运行可注释此行
            
        except Exception as e:
            err = traceback.format_exc()
            print(err)
            msg = '亚马逊到货爬虫异常！'
            logging(msg)
            fsmsg.send('亚马逊到货爬虫', msg, err)
            time.sleep(60)  # 异常后等待60秒再重试


# 常见问题解决方案和配置建议：

"""
1. 代理相关问题解决：
   - 如果代理连接失败，检查 proxy_type 参数是否正确
   - 确保 ProxyExtensionGenerator 模块可用
   - 检查代理池 IPPool 是否正常工作

2. 浏览器启动问题：
   - 确保端口未被占用，使用 get_chrome_free_port() 获取可用端口
   - 检查 Chrome 浏览器是否正确安装
   - 清理浏览器缓存：clear_plugin_cache_port(port)

3. 依赖模块检查：
   需要确保以下模块可用：
   - utils_mrc.pub_fun
   - work.自动化.亚马逊.Amazon
   - work.自动化.亚马逊.AmazonDP
   - utils_mrc.MysqlHelper
   - utils_mrc.FeiShuAPI

4. 推荐的初始化方式：

   # 方式1：使用 AmazonSK（Session方式，轻量级）
   amazon = AmazonSK(proxy_type=1, port=9444)

   # 方式2：使用 AmazonDP（浏览器方式，功能完整）
   amazon_dp = AmazonDP(proxy_type=1, local_port=9444)

5. 代理类型说明：
   - proxy_type=0: 不使用代理（本地IP）
   - proxy_type=1: 使用代理池1
   - proxy_type=2: 使用代理池2

6. 错误处理建议：
   - 添加重试机制
   - 记录详细的错误日志
   - 使用飞书通知异常情况
   - 定期检查代理可用性

7. 性能优化建议：
   - 合理设置请求间隔
   - 使用连接池复用连接
   - 定期清理浏览器缓存
   - 监控内存使用情况
"""
