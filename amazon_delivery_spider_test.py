# -*- coding:UTF-8 -*-
# @FileName  :amazon_delivery_spider_test.py
# @Time      :2024/12/20
# <AUTHOR>
# 简化测试版本，用于验证基本功能

import logging as std_logging
import time
import traceback

# 设置基本日志
std_logging.basicConfig(level=std_logging.INFO)
logger = std_logging.getLogger(__name__)

def test_imports():
    """测试所有必要的导入"""
    try:
        logger.info("开始测试导入模块...")
        
        # 测试基本模块
        import feapder
        logger.info("✓ feapder 导入成功")
        
        import pymysql
        logger.info("✓ pymysql 导入成功")
        
        from playwright.sync_api import sync_playwright
        logger.info("✓ playwright 导入成功")
        
        # 测试自定义模块
        try:
            from utils_mrc.pub_fun import *
            logger.info("✓ utils_mrc.pub_fun 导入成功")
        except ImportError as e:
            logger.warning(f"✗ utils_mrc.pub_fun 导入失败: {e}")
            
        try:
            from utils_mrc.SpiderTools import IPPool
            logger.info("✓ utils_mrc.SpiderTools.IPPool 导入成功")
            
            # 测试代理获取
            proxy_url = IPPool.proxies.get('http', '')
            if proxy_url:
                logger.info(f"✓ 代理获取成功: {proxy_url}")
            else:
                logger.warning("✗ 未获取到代理")
                
        except ImportError as e:
            logger.warning(f"✗ utils_mrc.SpiderTools 导入失败: {e}")
        except Exception as e:
            logger.warning(f"✗ 代理获取失败: {e}")
            
        try:
            from utils_mrc.MysqlHelper import MS, logging as mrc_logging
            logger.info("✓ utils_mrc.MysqlHelper 导入成功")
            
            # 测试数据库连接
            test_sql = "SELECT 1"
            result = MS.get_one(test_sql)
            if result:
                logger.info("✓ 数据库连接测试成功")
            else:
                logger.warning("✗ 数据库连接测试失败")
                
        except ImportError as e:
            logger.warning(f"✗ utils_mrc.MysqlHelper 导入失败: {e}")
        except Exception as e:
            logger.warning(f"✗ 数据库连接失败: {e}")
            
        try:
            from utils_mrc.FeiShuAPI import fsmsg
            logger.info("✓ utils_mrc.FeiShuAPI 导入成功")
        except ImportError as e:
            logger.warning(f"✗ utils_mrc.FeiShuAPI 导入失败: {e}")
            
        logger.info("模块导入测试完成")
        return True
        
    except Exception as e:
        logger.error(f"导入测试失败: {traceback.format_exc()}")
        return False

def test_browser():
    """测试浏览器启动"""
    try:
        logger.info("开始测试浏览器启动...")
        
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            browser = p.chromium.launch(
                headless=False,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-blink-features=AutomationControlled'
                ]
            )
            
            context = browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            )
            
            page = context.new_page()
            page.goto('https://www.baidu.com', timeout=30000)
            
            title = page.title()
            logger.info(f"✓ 浏览器启动成功，页面标题: {title}")
            
            browser.close()
            return True
            
    except Exception as e:
        logger.error(f"浏览器测试失败: {traceback.format_exc()}")
        return False

def test_proxy_browser():
    """测试带代理的浏览器启动"""
    try:
        logger.info("开始测试带代理的浏览器启动...")
        
        from utils_mrc.SpiderTools import IPPool
        from playwright.sync_api import sync_playwright
        from urllib.parse import urlparse
        
        proxy_url = IPPool.proxies.get('http', '')
        if not proxy_url:
            logger.warning("未获取到代理，跳过代理浏览器测试")
            return True
            
        p = urlparse(proxy_url)
        proxy_info = {
            "server": f"{p.scheme}://{p.hostname}:{p.port}",
            "username": p.username,
            "password": p.password
        }
        
        logger.info(f"使用代理: {proxy_info}")
        
        with sync_playwright() as playwright:
            browser = playwright.chromium.launch(
                headless=False,
                proxy=proxy_info,
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage'
                ]
            )
            
            context = browser.new_context()
            page = context.new_page()
            
            # 测试代理是否生效
            page.goto('https://httpbin.org/ip', timeout=30000)
            content = page.content()
            logger.info(f"✓ 代理浏览器启动成功，IP信息: {content[:200]}...")
            
            browser.close()
            return True
            
    except Exception as e:
        logger.error(f"代理浏览器测试失败: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    logger.info("=" * 50)
    logger.info("Amazon配送信息爬虫 - 环境测试")
    logger.info("=" * 50)
    
    # 测试1: 模块导入
    if not test_imports():
        logger.error("模块导入测试失败，请检查依赖")
        return False
    
    # 测试2: 基础浏览器
    if not test_browser():
        logger.error("浏览器测试失败，请检查Playwright安装")
        return False
    
    # 测试3: 代理浏览器
    if not test_proxy_browser():
        logger.error("代理浏览器测试失败，但可以继续使用本地IP")
    
    logger.info("=" * 50)
    logger.info("环境测试完成！可以运行主程序")
    logger.info("=" * 50)
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✓ 所有测试通过，可以运行主程序")
        else:
            print("\n✗ 测试失败，请根据日志修复问题")
    except Exception as e:
        print(f"\n✗ 测试程序异常: {e}")
        print(traceback.format_exc())
