# Amazon配送信息爬虫修改说明

## 主要修改内容

### 1. 导入模块修改
**原代码问题：**
```python
from utils_mrc import SpiderTools  # 错误的导入方式
```

**修改后：**
```python
from utils_mrc.pub_fun import *
from utils_mrc.SpiderTools import IPPool
from utils_mrc.MysqlHelper import logging as mrc_logging
from utils_mrc.FeiShuAPI import fsmsg
```

### 2. 代理配置优化
**原代码问题：**
- 直接访问 `IPPool.proxies['http']` 可能导致KeyError
- 缺少代理验证失败的降级处理

**修改后：**
```python
proxy_url = IPPool.proxies.get('http', '')
if not proxy_url:
    self.logger.warning("未获取到代理，将使用本地IP")
    self.use_proxy = False
else:
    # 验证代理可用性，失败时自动降级到本地IP
    try:
        # 代理验证逻辑
    except Exception as e:
        self.logger.warning(f"代理验证失败，将使用本地IP: {e}")
        self.use_proxy = False
```

### 3. 数据库连接优化
**原代码问题：**
- 硬编码数据库配置
- 没有使用现有代码库的数据库连接

**修改后：**
```python
try:
    from utils_mrc.MysqlHelper import MS
    self.ms = MS
    # 使用现有代码库的数据库连接
    results = self.ms.get_all(sql)
except ImportError:
    # 备用方案：使用原有的pymysql连接
    connection = pymysql.connect(**self.mysql_config)
```

### 4. 错误处理和日志记录
**新增功能：**
- 集成飞书通知系统
- 使用现有代码库的日志系统
- 添加详细的错误处理和降级机制

## 使用建议

### 1. 运行前检查
确保以下模块可用：
```python
from utils_mrc.pub_fun import *
from utils_mrc.SpiderTools import IPPool
from utils_mrc.MysqlHelper import MS, logging
from utils_mrc.FeiShuAPI import fsmsg
```

### 2. 代理配置
- 脚本会自动检测代理可用性
- 代理失败时自动降级到本地IP
- 可通过 `use_proxy=False` 参数强制不使用代理

### 3. 数据库配置
- 优先使用现有代码库的MS实例
- 如果MS不可用，会使用备用的pymysql连接
- 确保数据库表 `data_amazon_asin_attr` 存在

### 4. 线程配置
建议使用单线程避免Playwright线程冲突：
```python
spider = AmazonDeliverySpider(
    thread_count=1,  # 单线程
    use_proxy=True
)
```

## 常见问题解决

### 1. 代理连接失败
**现象：** 日志显示"代理验证失败"
**解决：** 
- 检查IPPool模块是否正常工作
- 脚本会自动降级到本地IP，可以继续运行

### 2. 数据库连接失败
**现象：** 日志显示"数据库连接错误"
**解决：**
- 检查MS模块是否可用
- 检查数据库配置是否正确
- 确保数据库表结构正确

### 3. 浏览器启动失败
**现象：** 日志显示"浏览器启动失败"
**解决：**
- 检查Playwright是否正确安装
- 确保Chrome浏览器可用
- 检查系统资源是否充足

### 4. 页面解析失败
**现象：** 大量ASIN解析失败
**解决：**
- 检查Amazon页面结构是否变化
- 调整XPath选择器
- 增加页面等待时间

## 性能优化建议

### 1. 内存管理
- 使用单线程避免内存泄漏
- 及时关闭页面和浏览器实例
- 定期重启爬虫进程

### 2. 请求频率控制
- 在页面访问间添加随机延迟
- 避免过于频繁的请求
- 监控IP是否被封禁

### 3. 错误恢复
- 实现自动重试机制
- 记录失败的ASIN供后续处理
- 定期检查爬虫运行状态

## 监控和维护

### 1. 日志监控
- 关注代理切换频率
- 监控页面解析成功率
- 检查数据库写入状态

### 2. 飞书通知
- 爬虫启动/停止通知
- 异常情况实时告警
- 定期运行状态报告

### 3. 数据质量检查
- 定期检查抓取数据的完整性
- 验证配送信息的准确性
- 监控数据更新频率
