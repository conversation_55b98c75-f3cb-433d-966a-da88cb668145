数据库结构
CREATE TABLE `data_lingxing_shipment_tracking` (
  `id` int(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `msku` varchar(100) NOT NULL DEFAULT '' COMMENT 'MSKU，商品的唯一标识',
  `fnsku` varchar(100) NOT NULL DEFAULT '' COMMENT 'FNSKU，亚马逊物流的唯一标识',
  `asin` varchar(20) NOT NULL DEFAULT '' COMMENT 'ASIN，亚马逊商品标识',
  `shipment_id` varchar(50) NOT NULL DEFAULT '' COMMENT '货件编号，FBA货件的唯一标识',
  `shipment_name` varchar(255) NOT NULL DEFAULT '' COMMENT '货件名称，用于描述货件内容',
  `tracking_number` varchar(100) NOT NULL DEFAULT '' COMMENT '追踪编号，用于物流追踪',
  `store` varchar(50) NOT NULL DEFAULT '' COMMENT '店铺名称',
  `country` varchar(50) NOT NULL DEFAULT '' COMMENT '国家/目的地国家',
  `declared_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '申报量，计划发货的商品数量',
  `shipped_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '已发货量，实际发货的商品数量',
  `received_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '签收量，已签收的商品数量',
  `pending_diagnosis_quantity` int(11) NOT NULL DEFAULT '0' COMMENT '预计可诊断数量，待确认的商品数量',
  `pending_diagnosis_amount` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '预计可诊断金额，待确认的金额',
  `remaining_diagnosis_days` int(11) NOT NULL DEFAULT '0' COMMENT '剩余可诊断天数，诊断的剩余时间',
  `status` varchar(50) NOT NULL DEFAULT '' COMMENT '状态，当前货件诊断的状态',
  `remark` varchar(512) NOT NULL DEFAULT '' COMMENT '备注，额外信息或说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `shipment_id` (`shipment_id`) USING BTREE,
  KEY `msku` (`msku`) USING BTREE,
  KEY `asin` (`asin`) USING BTREE,
  KEY `update_time` (`update_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='货件签收差异记录表';


列表页请求接口：
json_data = {
    'sid_list': [],
    'mid_list': [],
    'offset': 0,
    'length': 200,
    'req_time_sequence': '/api/tool/shipment_claim/getSummaryBody$$5',
}

response = requests.post(
    'https://muke.lingxing.com/api/tool/shipment_claim/getSummaryBody',
    cookies=cookies,
    headers=headers,
    json=json_data,
)
返回结果：
{
    "code": 1,
    "msg": "操作成功",
    "require_id": "87668BD0-AF31-DD14-5C16-82CB9D55099A",
    "data": {
        "total": 146,
        "list": [
            {
                "sid": 2361,
                "mid": 4,
                "sellerName": "AM_LBZ-UK",
                "countryName": "英国",
                "estimateDiagnostic": {
                    "diagnosticTotalAmount": "2.83",
                    "diagnosticTotalNum": "12",
                    "shipmentIdTotalCount": 2,
                    "currencyIcon": "$"
                },
                "underDiagnostic": {
                    "diagnosticTotalAmount": "0.00",
                    "diagnosticTotalNum": "0",
                    "shipmentIdTotalCount": 0,
                    "currencyIcon": "$"
                },
                "diagnosed": {
                    "diagnosticTotalAmount": "0.00",
                    "diagnosticTotalNum": "0",
                    "shipmentIdTotalCount": 0,
                    "currencyIcon": "$"
                },
                "caseIdCount": 0
            },
            {
                "sid": 3159,
                "mid": 6,
                "sellerName": "AM_ZZY-FR",
                "countryName": "法国",
                "estimateDiagnostic": {
                    "diagnosticTotalAmount": "11.57",
                    "diagnosticTotalNum": "12",
                    "shipmentIdTotalCount": 7,
                    "currencyIcon": "$"
                },
                "underDiagnostic": {
                    "diagnosticTotalAmount": "0.00",
                    "diagnosticTotalNum": "0",
                    "shipmentIdTotalCount": 0,
                    "currencyIcon": "$"
                },
                "diagnosed": {
                    "diagnosticTotalAmount": "0.00",
                    "diagnosticTotalNum": "0",
                    "shipmentIdTotalCount": 0,
                    "currencyIcon": "$"
                },
                "caseIdCount": 0
            },
            ......
        ]
    },
    "req_time_sequence": "/api/tool/shipment_claim/getSummaryBody$$5",
    "update_code": 0
}

详情页请求接口：
json_data = {
    'sid': 2361,
    'sort_field': '',
    'sort_type': '',
    'offset': 0,
    'length': 200,
    'status': 0,
    'search_value': '',
    'search_field': 'msku',
    'req_time_sequence': '/api/tool/shipment_claim/list$$12',
}

response = requests.post(
    'https://muke.lingxing.com/api/tool/shipment_claim/list',
    cookies=cookies,
    headers=headers,
    json=json_data,
)
返回结果：
{
    "code": 1,
    "msg": "操作成功",
    "require_id": "8B1CC500-F88F-A825-356E-161502F53650",
    "data": {
        "total": 3,
        "list": [
            {
                "id": "250586395712413696",
                "mid": 4,
                "sid": 2361,
                "shipmentId": "FBA15KD60NHV",
                "shipmentName": "运营四部LBZ-佳世达-11-0523",
                "shipmentReferenceId": "8EBIPIDR",
                "fnsku": "X00232BKT1",
                "msku": "ALBZ05-H-YMS-XHL203",
                "asin": "B0D9NNWMMH",
                "asinUrl": "https://www.amazon.co.uk/dp/B0D9NNWMMH",
                "status": 0,
                "caseId": "",
                "remark": "",
                "diagnosticDateTime": null,
                "estimateDiagnosticAmount": "0.7893",
                "estimateDiagnosticNum": 1,
                "estimateRemainDaysForDiagnostic": 48,
                "shipmentQuantityShipped": 20,
                "shipmentQuantityReceived": 19,
                "shipmentQuantityShippedLocal": 20,
                "currencyIcon": "$",
                "currencyIconForReimbursed": "",
                "amountReimbursedInventory": "0.00",
                "amountReimbursedCash": "0.00",
                "amountReimbursedTotal": "0.00",
                "quantityReimbursedCash": 0,
                "quantityReimbursedInventory": 0,
                "quantityReimbursedTotal": 0,
                "picUrl": "https://image.distributetop.com/lingxing-erp/901122764666708480/20240711/777e2dde0c0d4429a82ab6f9e8db64c5.jpg",
                "countryName": "英国",
                "sellerName": "AM_LBZ-UK",
                "statusCnName": "未诊断",
                "localStaId": "231574587754104320",
                "localStaName": "运营四部LBZ-佳世达-11-0523"
            },
            {
                "id": "250586395712349184",
                "mid": 4,
                "sid": 2361,
                "shipmentId": "FBA15KD60NHV",
                "shipmentName": "运营四部LBZ-佳世达-11-0523",
                "shipmentReferenceId": "8EBIPIDR",
                "fnsku": "X002540STZ",
                "msku": "ALBZ05-F-UK-YSK02",
                "asin": "B0DHY1G2KJ",
                "asinUrl": "https://www.amazon.co.uk/dp/B0DHY1G2KJ",
                "status": 0,
                "caseId": "",
                "remark": "",
                "diagnosticDateTime": null,
                "estimateDiagnosticAmount": "0.3157",
                "estimateDiagnosticNum": 1,
                "estimateRemainDaysForDiagnostic": 48,
                "shipmentQuantityShipped": 15,
                "shipmentQuantityReceived": 14,
                "shipmentQuantityShippedLocal": 15,
                "currencyIcon": "$",
                "currencyIconForReimbursed": "",
                "amountReimbursedInventory": "0.00",
                "amountReimbursedCash": "0.00",
                "amountReimbursedTotal": "0.00",
                "quantityReimbursedCash": 0,
                "quantityReimbursedInventory": 0,
                "quantityReimbursedTotal": 0,
                "picUrl": "https://image.distributetop.com/erp-vue/901122764666708480/20240229/7fa719928bb94beb8dde483f81938027.jpg",
                "countryName": "英国",
                "sellerName": "AM_LBZ-UK",
                "statusCnName": "未诊断",
                "localStaId": "231574587754104320",
                "localStaName": "运营四部LBZ-佳世达-11-0523"
            },
            ......
        ]
    },
    "req_time_sequence": "/api/tool/shipment_claim/list$$12",
    "update_code": 0
}