@echo off
chcp 65001 >nul
echo ========================================
echo 飞书接口测试工具
echo ========================================
echo.
echo 请选择测试类型:
echo 1. 简单测试（快速诊断）
echo 2. 完整测试（详细功能测试）
echo 3. 退出
echo.
set /p choice=请输入选择 (1-3): 

if "%choice%"=="1" (
    echo.
    echo 开始简单测试...
    python "任务系统\simple_feishu_test.py"
) else if "%choice%"=="2" (
    echo.
    echo 开始完整测试...
    python "任务系统\test_feishu_api.py"
) else if "%choice%"=="3" (
    echo 退出程序
    exit /b 0
) else (
    echo 无效选择，请重新运行脚本
)

echo.
pause
