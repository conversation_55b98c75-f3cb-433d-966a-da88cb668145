# -*- coding:UTF-8 -*-
# @FileName  :AmazonLoc.py
# @Time      :2024/12/13 10:18
# <AUTHOR>
import json
import re

from utils_mrc.pub_fun import *
from tqdm import tqdm
from work.自动化.亚马逊.AmazonSessionPage import *
from work.自动化.卖家精灵.SellerSpriteExpansion import sse
from work.自动化.西柚.XiYouZhaoCi import xyzc

import requests

_orig_request = requests.Session.request

def log_request(self, method, url, **kwargs):
    print(f"[全局代理请求] method={method}, url={url}, proxies={kwargs.get('proxies')}")
    try:
        resp = _orig_request(self, method, url, **kwargs)
        print(f"[全局代理响应] 状态码: {resp.status_code}, 内容: {resp.text[:200]}")
        return resp
    except Exception as e:
        print(f"[全局代理异常] {e}")
        raise

requests.Session.request = log_request


class AmazonSK(AmazonSessionPage):
    def __init__(self, port=None, proxy_type=1):
        super().__init__(port, proxy_type)
        self.max_total = 10000
        self.asin_locator = '@@data-asin^B@@data-index@@data-component-type=s-search-result'

    def get_asins_loc_index(self, url_param, pages_param=3):
        # 每次抓取前，还原本次任务基本抓取状态
        self.cur_info = init_fetch_info()
        t1 = time.time()
        logging(f'开始执行任务')
        self.cur_info['cur_page'] = 1
        datas = []
        url_keyword = url_get_keyword(url_param)
        try:
            if not url_keyword:
                self.cur_info['status'] = 99
                raise Exception("搜索关键词不能为空")
            elif ',' in url_param:
                self.cur_info['status'] = 99
                raise Exception("检测到url包含逗号，请修正数据！")
            with tqdm(total=pages_param, desc=f'数据采集 {url_param}', unit='页', file=sys.stdout) as pbar:
                while True:
                    t2 = time.time()
                    if pages_param != 1:  # 如果不指定页数，抓取地址页
                        url_param = update_url_param(url_param, 'page', self.cur_info['cur_page'])
                    self.cur_info['url'] = url_param
                    pbar.set_description(f'正在采集 {url_param} 第 {self.cur_info["cur_page"]} 页')  # 更新进度条描述
                    self.cur_info['real_page'] = self.cur_info["cur_page"]

                    @retry_on_exception(msg='广告位解析异常')
                    def get_data(self):
                        self.goto(url_param)
                        datas_ = []
                        ele_asins = self.page.eles(self.asin_locator)
                        if not ele_asins:
                            raise Exception('未找到ASIN元素')
                        site = self.get_site_by_url()
                        for loc_index, div in enumerate(ele_asins, start=1):
                            cur_dict = {}
                            asin = div.attr('data-asin')
                            self.cur_info['all_loc_index'] += 1
                            is_ad = 1 if 'aok-inline-block puis-sponsored-label-info-icon' in div.inner_html else 0  # 广告
                            ele_price = div('@data-cy=price-recipe', timeout=2)
                            if 'reviews-block' in div.inner_html:
                                if 'a-color-secondary' in div('@data-cy=reviews-block').inner_html:
                                    ztxl = div('@data-cy=reviews-block')('c:.a-color-secondary')
                                    ztxl_text = ztxl.text if ztxl else '0'
                                    child_sales = extract_number_sales(ztxl_text)
                                    cur_dict['child_sales'] = child_sales
                                if ele_price and 'sx-red-mvt' in ele_price.inner_html:
                                    tag_spike = 1
                                    cur_dict['tag_spike'] = tag_spike
                            if ele_price and 's-coupon-component' in ele_price.inner_html:
                                yh = ele_price('c:.s-coupon-unclipped>span')
                                coupon = yh.text if yh else ''
                                cur_dict['coupon'] = coupon

                            e_title = div.ele('t:h2')
                            e_price = div.ele('c=[class="a-offscreen"]')
                            e_image = div.ele('c=img.s-image')
                            if not e_price or not e_title or not e_image:
                                continue
                            title = e_title.text
                            symbol, price = separate_currency_price(e_price.text)
                            img = div.ele('c=img.s-image').attr('src')

                            e_score = div.ele('.a-icon-alt')
                            stars = e_score.text.split()[0].replace(',', '.') if e_score else 0

                            cur_dict.update({
                                'asin': asin,
                                'page_index': self.cur_info["cur_page"],
                                'loc_index': loc_index,
                                'all_loc_index': self.cur_info['all_loc_index'],
                                'is_ad': is_ad,
                                'url': self.cur_info['url'],
                                'site': site,
                                'page_count': len(ele_asins),
                                'title': title,
                                'price': price,
                                'img': img,
                                'keywords': url_keyword,
                                'stars': stars,
                            })
                            datas_.append(cur_dict)
                        return datas_

                    datas_ = get_data(self)
                    datas.extend(datas_)
                    self.cur_info['total'] = len(datas)

                    # 更新进度条后缀
                    pbar.set_postfix({
                        f'第{self.cur_info["cur_page"]}页数量': len(datas_),
                        '当前总数量': len(datas),
                        '当前asin总数量': len(datas),
                        '用时': f'{time.time() - t2:.2f}s',
                        '总耗时': f'{time.time() - t1:.2f}s'
                    })
                    pbar.update(1)  # 更新进度条
                    self.cur_info['cur_page'] += 1
                    if self.cur_info['cur_page'] > pages_param or self.cur_info['total'] >= self.max_total:
                        break
        except Exception as e:
            err_info = traceback.format_exc()
            self.cur_info['result'] += f' |抓取异常:[第{self.cur_info["cur_page"]}页] {e}'
            self.cur_info['status'] = max(self.cur_info['status'], 20)
            print(err_info)
            fsmsg.send('亚马逊数据采集', self.cur_info['result'], err_info)

        print(f'爬取结束,总页数{self.cur_info["real_page"]},总asin数量: {len(datas)},总用时:{time.time() - t1}')

        self.cur_info['result'] += f'| 实抓页数:{self.cur_info["real_page"]}'
        return datas

    def get_asins_by_url_pages(self, url_param, pages_param=1):
        # 每次抓取前，还原本次任务基本抓取状态
        self.cur_info = init_fetch_info()
        if pages_param <= 0:  # 默认抓取前20页
            pages_param = 999
        t1 = time.time()
        logging(f'开始计时,当前时间: {datetime.today()}')
        self.cur_info['cur_page'] = 1
        datas = []
        try:
            with tqdm(total=pages_param, desc=f'数据采集 {url_param}', unit='页', file=sys.stdout) as pbar:
                while True:
                    if self.cur_info['cur_page'] > pages_param or self.cur_info['total'] >= self.max_total or '未返回结果' in self.cur_info['result']:
                        print(f'超过抓取上限，终止')
                        pbar.close()
                        break
                    t2 = time.time()
                    if pages_param != 1:  # 如果不指定页数，抓取地址页
                        url_param = update_url_param(url_param, 'page', self.cur_info['cur_page'])
                    self.cur_info['url'] = url_param
                    pbar.set_description(f'正在采集 {url_param} 第 {self.cur_info["cur_page"]} 页')  # 更新进度条描述
                    self.cur_info['real_page'] = self.cur_info["cur_page"]
                    self.goto(url_param)

                    datas_ = self.parse_of_page_list()
                    datas.extend(datas_)
                    self.cur_info['total'] = len(datas)

                    # 更新进度条后缀
                    pbar.set_postfix({
                        f'第{self.cur_info["cur_page"]}页数量': len(datas_),
                        '当前总数量': len(datas),
                        '当前asin总数量': len(datas),
                        '用时': f'{time.time() - t2:.2f}s',
                        '总耗时': f'{time.time() - t1:.2f}s'
                    })

                    pbar.update(1)  # 更新进度条
                    self.cur_info['cur_page'] += 1
        except Exception as e:
            err_info = traceback.format_exc()
            self.cur_info['result'] += f' |抓取异常:[第{self.cur_info["cur_page"]}页] {e}'
            self.cur_info['status'] = 20
            print(err_info)
            # fsmsg.send('亚马逊数据采集', self.cur_info['result'], err_info)

        print(f'爬取结束,总页数{self.cur_info["real_page"]},总asin数量: {len(datas)},总用时:{time.time() - t1}')
        self.cur_info['result'] += f'| 实抓页数:{self.cur_info["real_page"]}'
        return datas

    @retry_on_exception()
    def parse_of_page_list(self):
        """
        解析页面列表中的数据。

        该方法从页面中提取特定元素的信息，包括商品的ASIN、标题、评分、评论数、价格、图片URL、配送方式和卖家数量。
        它处理每个页面直到所有相关数据被提取完毕。

        Returns:
            list: 包含所有提取到的数据的列表，每个元素是一个字典，代表一个商品的详细信息。
        """
        # 初始化一个空列表，用于存储解析的数据
        datas = []
        # 定义定位器，用于查找特定元素 # [data-asin^="B"][data-uuid][data-index][data-component-type="s-search-result"]

        ele_divs = self.page.eles(self.asin_locator)  # 先获取所有asin数量
        if 16 > len(ele_divs) > 0:
            ad_divs = self.page.eles('c=[class="aok-inline-block puis-sponsored-label-info-icon"]')
            if len(ad_divs) == len(ele_divs):
                self.cur_info['result'] = f' |[第{self.cur_info["cur_page"]}页] 搜索页面未返回结果'
                self.cur_info['status'] = 10
                # logging(self.cur_info['result'])
                return datas
        self.cur_info['page_count'] = self.cur_info['page_count'] or len(ele_divs)
        asins_list = ele_divs.filter.get.attrs('data-asin')
        if not asins_list or not ele_divs:
            raise Exception('asins获取失败')
        site = self.get_site_by_url()
        api_result = sse.get_list(site, asins_list) or {}  # 源数据
        api_items = (api_result.get('data') or {}).get('items') or []  # ["data"]["items"]
        api_data_dict = {item.get('asin') or '': item for item in api_items} or {}  # 取asin组成键值对
        if not api_data_dict:
            self.cur_info['result'] += f' |[第{self.cur_info["cur_page"]}页]插件数据获取失败：{api_result}'
            self.cur_info['status'] = 20
            logging(self.cur_info['result'])
            fsmsg.send('亚马逊选品任务采集', f'卖家精灵接口获取失败 asins：{asins_list}', self.cur_info['result'])
        # 遍历每个元素，提取其中的数据
        for c_i, div in enumerate(ele_divs):
            data = {}
            # 从元素中提取ASIN、标题、评分、评论数、价格、图片URL等信息
            asin = div.attr('data-asin').strip()
            title = div.ele('t:h2').text
            e_score = div.ele('.a-icon-alt')
            stars = e_score.text.split()[0].replace(',', '.') if e_score else 0
            e_comment_num = div.ele('c=[class="a-size-base s-underline-text"]')
            ratings = e_comment_num.text.replace(',', '') if e_comment_num else 0
            e_price = div.ele('c=[class="a-offscreen"]')
            if not e_price:
                continue
            symbol, price = separate_currency_price(e_price.text)
            currency = AmazonConfig.CURRENCYS.get(symbol, f'未识别{symbol}')
            image = div.ele('c=img.s-image').attr('src')
            try:
                asin_datas = api_data_dict.get(asin) or {}
                seller_type = asin_datas.get('sellerType') or asin_datas.get('seller_type') or ''
                sellers = asin_datas.get('sellers') or 0
                seller_id = asin_datas.get('seller_id') or ''
                seller_url = f"{'/'.join(self.page.url.split('/', )[:3])}/sp?seller={seller_id}" if seller_id else ''

                bsrList = asin_datas.get('bsrList') or []
                if bsrList:
                    brs_json = [
                        {'code': int(br.get('id')) if br.get('id').isdigit() else 0,
                         'rank': br.get('rank') or 0,
                         'label': br.get('label') or ''}
                        for br in bsrList if br.get('id').isdigit()
                    ]
                    # 使用 min 函数找到 rank 最小的元素，max 找到最大的
                    min_rank_br = min(brs_json, key=lambda x: x['rank'], default={})
                    max_rank_br = max(brs_json, key=lambda x: x['rank'], default={})

                    # 更新 data 字典，仅当存在多个元素时才更新小类信息
                    data.update({
                        'category_id': min_rank_br.get('code', ''),
                        'category_name': min_rank_br.get('label', ''),
                        'bsr_rank': min_rank_br.get('rank', 0),
                        # 只有当 brs_json 包含多于一个元素时才设置小类信息
                        'subcategory': max_rank_br.get('label', '') if len(brs_json) > 1 else '',
                        'subcategory_rank': max_rank_br.get('rank', '') if len(brs_json) > 1 else '',
                        'subcategory_code': max_rank_br.get('code', '') if len(brs_json) > 1 else '',
                    })
                else:
                    brs_json = []
                    e_brs = div.eles(f'c=div[data-asin^="{asin}"] .bsr-list-item', timeout=2)
                    if e_brs:
                        for e_b in e_brs:
                            brs_label = e_b.child(2).text.strip()
                            rank = extract_number(e_b.child().child().text.replace(',', ''))
                            brs_json.append({'label': brs_label, 'rank': rank})
                        # if len(brs_json) >= 2:
                        data['category_name'] = brs_json[0].get('label', '')
                        data['bsr_rank'] = brs_json[0].get('rank', '')
                        data['subcategory'] = brs_json[-1].get('label', '')
                        data['subcategory_rank'] = brs_json[-1].get('rank', '')
                brs_json_str = json.dumps(brs_json, ensure_ascii=False, separators=(",", ":"))
                # 将提取到的信息存储到字典中
                data['asin'] = asin
                data['title'] = title
                data['stars'] = stars
                data['ratings'] = ratings
                data['price'] = price
                data['currency'] = currency
                data['image'] = image
                data['seller_type'] = seller_type
                data['sellers'] = sellers
                data['seller_url'] = seller_url
                data['brs_json_str'] = brs_json_str
                data['url'] = f"{'/'.join(self.page.url.split('/', )[:3])}/dp/{asin}?psc=1"
                data['brand'] = asin_datas.get('brand') or ''
                data['seller_name'] = asin_datas.get('sellerName') or asin_datas.get('seller_name') or ''
                data['units'] = asin_datas.get('units') or 0
                data['month_units'] = asin_datas.get('month_units') or 0
                data['amount'] = asin_datas.get('amount') or 0
                data['variations'] = asin_datas.get('variations') or 0
                data['weight'] = asin_datas.get('weight') or ''
                data['dimension'] = asin_datas.get('dimension') or ''
                available = asin_datas.get('available') or 0
                data['available'] = int(available) / 1000 if len(str(available)) == 13 else available

                data['parent'] = asin_datas.get('parent') or ''
                data['fba'] = asin_datas.get('fba') or ''
                data['profit'] = asin_datas.get('profit') or ''
                data['seller_location'] = (asin_datas.get('sellerDto') or {}).get('nationCode') or ''
                datas.append(data)
            except Exception as e:
                # 如果在解析过程中遇到异常，打印异常信息
                traceback.print_exc()
                print(e)
        # 返回包含所有数据的列表
        return datas


class AmazonListing(AmazonSessionPage):
    def __init__(self, port=None, proxy_type=1):
        super().__init__(port, proxy_type)
        self.page = SessionPage(tls_session)
        self.page.set.headers(super().headers)
        self.init_cookies()

    def fetch_search_image_price(self, content, site='') -> list:
        self.cur_info = init_fetch_info()

        @retry_on_exception()
        def get_data(self):
            self.goto(content)
            # img = self.page('c=[data-csa-c-action="image-block-main-image-hover"] img')
            img = self.page('c=[data-old-hires]')
            src = img.attr('src')
            img_data = self.page.session.get(src).content
            return img_data

        site = site or self.get_site_by_url(content)
        img_data = get_data(self)
        results = self.parse_search_image_data(img_data)

        def update(x: dict):
            x['site'] = site
            x['currency'], x['price'] = separate_currency_price(x['price'])
            try:
                del x['colorSwatches']
                del x['twisterVariations']
            except: pass
            return x

        results = [update(x) for x in results]
        return results

    def parse_search_image_data(self, img_data):
        if not img_data:
            logging(f'未传入图片数据！')
            return []
        href = '/'.join(self.page.url.split('/', )[:3])
        search_url = f'{href}/stylesnap?q=local'
        self.page.get(search_url)
        token = self.page('@name=stylesnap').attr('value')
        params = {
            "stylesnapToken": token
        }
        files = {'explore-looks.jpg': ('explore-looks.jpg', img_data, 'image/jpeg')}
        self.page.post(f'{href}/stylesnap/upload', params=params, files=files)
        result = self.page.json or {}
        searchResults = result.get('searchResults')
        if not searchResults:
            logging(f'搜索失败！')
            print(result)
            return []
        data_list = searchResults[0].get('bbxAsinMetadataList')
        # data_list = convert_keys_to_snake_case(original_data_list)
        return data_list

    def check_asin_listing(self, asins, site, app_id=5):
        self.cur_info = init_fetch_info()
        datas = []
        asin_list = split_get_list(asins)
        if not asin_list:
            self.cur_info['result'] += f' |无效的输入类型，asins请输入字符串或列表'
            self.cur_info['status'] = 20
            return datas
        asin_list = [asin for asin in asin_list if asin]
        for index, asin in enumerate(tqdm(asin_list, desc='检查详情页状态', unit='页', file=sys.stdout)):
            if len(asin.strip()) > 10:
                self.cur_info['result'] += f' |无效的输入类型，asin长度不能超过10位'
                self.cur_info['status'] = 20
                return datas
            row = {
                'status': 2  # '获取异常'
            }
            site_code = AmazonConfig.get_amz_site(site)

            url = f'https://www.amazon.{site_code}/dp/{asin}?th=1&psc=1'
            self.cur_info['cur_asin_result'] = ''
            self.goto(url)

            @retry_on_exception(msg=f'[{asin} {site}]抓取异常。')
            def get_data(self):
                if '404页面' in self.cur_info['cur_asin_result']:
                    row['status'] = 99  # '被删除' 下架
                    return
                self.reset_language()
                # 标题
                title = self.page('c=span#productTitle').text
                if title:
                    row['status'] = 10  # '正常' 可售
                row['title'] = title
                if 'id="atc-declarative"' not in self.page.html:
                    row['status'] = 20  # '不可售'
                e_seller = self.page('c=#merchantInfoFeature_feature_div .offer-display-feature-text.a-spacing-none ')
                if e_seller:
                    row['platform_account'] = e_seller.text

                e_techSpec = self.page.eles('c=#productDetails_techSpec_section_1 tr')  # 技术规格
                if e_techSpec:
                    row['tech_spec'] = {}
                    for e_t in e_techSpec:
                        label = e_t('t:th').text.strip()
                        value = e_t('t:td').text.strip()
                        row['tech_spec'][label] = value
                e_productDesc = self.page('c=div#productDescription_feature_div')  # 产品说明
                if e_productDesc:
                    row['product_desc'] = e_productDesc.text.strip()
                e_main_img = self.page('c=#imgTagWrapperId img')  # 主图链接
                if e_main_img:
                    row['img_url'] = e_main_img.attr('src')

                if app_id == 8:
                    # 五点
                    items = self.page.eles('c=div#feature-bullets ul li span', timeout=3)
                    if not items:
                        items = self.page.eles('c=div#productFactsDesktopExpander ul li span', timeout=3)
                    for i1, item in enumerate(items):
                        row[f'short_desc{i1 + 1}'] = item.text
                    # 产品描述
                    try:
                        # detail = self.page('c=div#detailBulletsWrapper_feature_div', timeout=3).text or '暂无'
                        detail = self.page('c=div#productDescription', timeout=3) or self.page('c=div#aplus', timeout=3)
                        detail = detail.text.strip() or '暂无'
                    except:
                        # tqdm.write(f'获取详情失败,url：{url}')
                        detail = '未找到'
                    row['detail'] = detail
                    # tqdm.write(f'正在检索流量词...')
                    keywordCns = xyzc.fetch_search_terms(asin, site)
                    row['keywords'] = keywordCns

                # sellerProfileTriggerId
                e_sellerProfile = self.page('#sellerProfileTriggerId')
                if e_sellerProfile:
                    href = e_sellerProfile.attr('href')
                    if href:
                        self.goto(href)
                        # 版本说明和卖家信息
                        e_detailSellerInfo = self.page('#page-section-detail-seller-info')
                        if e_detailSellerInfo:
                            row['detail_seller_info'] = e_detailSellerInfo.text.strip()

            get_data(self)
            row.update({
                'site': site,
                'asin': asin,
                'has_cart': 1 if row['status'] == 10 else 0,
                'url': url,
            })
            datas.append(row)
            if len(datas) % 100 == 0:  # 每100个存一次数据库
                # print(f"达到 {len(datas)} 个元素,执行额外处理！")
                # print(f"当前时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())}")
                insert_amazon_listing(datas, self.task_params, 'data_amazon_asin_attr')

        self.cur_info['result'] = '成功' if self.cur_info['status'] == 10 else self.cur_info['result']
        return datas

    def get_listing_detail(self, asins, site):
        datas = []
        asin_list = split_get_list(asins)
        if not asin_list:
            self.cur_info['result'] += f' |无效的输入类型，asins请输入字符串或列表'
            self.cur_info['status'] = 20
            return datas
        asin_list = [asin for asin in asin_list if asin]
        for index, asin in enumerate(tqdm(asin_list, desc='采集详情页', unit='页', file=sys.stdout)):
            if len(asin.strip()) > 10:
                self.cur_info['result'] += f' |无效的输入类型，asin长度不能超过10位'
                self.cur_info['status'] = 20
                return datas
            row = {
                'status': 2  # '获取异常'
            }
            site_code = AmazonConfig.get_amz_site(site)

            url = f'https://www.amazon.{site_code}/dp/{asin}'
            self.cur_info['cur_asin_result'] = ''
            self.goto(url)

            html = self.page.html
            with open(rf'D:\Documents\Downloads\{asin}.html', 'w', encoding='utf-8') as f:
                f.write(html)
                print('网页源代码已保存。')

            row = {}

            @retry_on_exception(msg=f'[{asin} {site}]抓取异常。')
            def get_data(self):
                # 1. 产品名称
                title = ''
                e_title = self.page('c=#productTitle')
                if e_title:
                    title = e_title.text.strip()
                row['title'] = title

                # 2. 五点描述
                bullet_points = []
                e_bullets = self.page.eles('css=#productFactsDesktopExpander .a-list-item') or []
                if e_bullets:
                    for bullet in e_bullets:
                        bullet_text = bullet.text.strip()
                        if bullet_text:
                            bullet_points.append(bullet_text)
                row['bullet_points'] = bullet_points

                # 3. 变体ASIN
                variant_asins = []
                e_variants = self.page.eles('c=[data-feature-name="softlinesTwister"] li[data-asin]') or []
                for variant in e_variants:
                    variant_asins.append(variant.attr('data-asin'))
                row['variant_asins'] = variant_asins

                # 4. 变体详情
                variant_details = {}
                e_dim_labels = self.page.eles('css=#variation_dimension_name_1, #variation_color_name, #variation_size_name') or []
                for label in e_dim_labels:
                    label_name = label.attr('id').replace('variation_', '').replace('_name', '')
                    selected = self.page(f'css=#{label.attr("id")} .selection')
                    if selected:
                        variant_details[label_name] = selected.text.strip()
                row['variant_details'] = variant_details

                # 5. ASIN Listing月销量
                row['monthly_sales'] = "无法从页面直接获取"

                # 6.销量变化率
                row['sales_change_rate'] = "无法从页面直接获取"

                # 7. 销量变化率更新时间
                row['sales_change_update_time'] = "无法从页面直接获取"

                # 8. Listing月销额
                row['monthly_revenue'] = "无法从页面直接获取"

                # 9. 实际价格
                price = ""
                e_price = self.page('css=#priceblock_ourprice, #priceblock_dealprice, .a-price .a-offscreen')
                if e_price:
                    price = e_price.text.replace('$', '').strip()
                row['price'] = price

                # 10. 类目1
                category1 = ""
                e_breadcrumbs = self.page.eles('css=#wayfinding-breadcrumbs_feature_div .a-link-normal') or []
                if len(e_breadcrumbs) > 0:
                    category1 = e_breadcrumbs[0].text.strip()
                row['category1'] = category1

                # 11. 类目1排名
                category1_rank = ""
                e_rank = self.page('css=#productDetails_detailBullets_sections1 th:contains("Best Sellers Rank")+td')
                if e_rank:
                    rank_text = e_rank.text
                    rank_match = re.search(r'#([\d,]+) in ([^(]+)', rank_text)
                    if rank_match:
                        category1_rank = rank_match.group(1).replace(',', '')
                row['category1_rank'] = category1_rank

                # 12. 类目2
                category2 = ""
                if len(e_breadcrumbs) > 1:
                    category2 = e_breadcrumbs[1].text.strip()
                row['category2'] = category2

                # 13. 类目2排名
                category2_rank = ""
                if e_rank:
                    rank_text = e_rank.text
                    rank_matches = re.findall(r'#([\d,]+) in ([^(]+)', rank_text)
                    if len(rank_matches) > 1:
                        category2_rank = rank_matches[1][0].replace(',', '')
                row['category2_rank'] = category2_rank

                # 14. 价格变化率
                row['price_change_rate'] = "无法从页面直接获取"

                # 15. 价格变化率更新时间
                row['price_change_update_time'] = "无法从页面直接获取"

                # 16. 旺季
                row['peak_season'] = "无法从页面直接获取"

                # 17. 划线价
                list_price = ""
                e_list_price = self.page('css=#price .a-text-strike, .a-price.a-text-price .a-offscreen')
                if e_list_price:
                    list_price = e_list_price.text.replace('$', '').strip()
                row['list_price'] = list_price

                # 18. 品牌
                brand = ""
                e_brand = self.page('css=#bylineInfo, #brand')
                if e_brand:
                    brand = e_brand.text.replace('Brand: ', '').strip()
                row['brand'] = brand

                # 19. BBX卖家属性
                row['bbx_seller_attribute'] = "无法从页面直接获取"

                # 20. 店铺名称
                seller_name = ""
                e_seller = self.page('css=#merchant-info a, #sellerProfileTriggerId')
                if e_seller:
                    seller_name = e_seller.text.strip()
                row['seller_name'] = seller_name

                # 21. 卖家地区
                row['seller_region'] = "无法从页面直接获取"

                # 22. 毛利
                row['gross_profit'] = "无法从页面直接获取"

                # 23. 毛利率
                row['gross_profit_rate'] = "无法从页面直接获取"

                # 24. 上架时间
                row['listing_date'] = "无法从页面直接获取"

                # 25. 上架时长
                row['listing_duration'] = "无法从页面直接获取"

                # 26. 好评率
                positive_rate = ""
                e_ratings = self.page.eles('css=#histogramTable .a-text-right') or []
                if e_ratings and len(e_ratings) >= 3:
                    positive_rate_text = e_ratings[0].text + e_ratings[1].text
                    positive_rate_match = re.search(r'(\d+)%', positive_rate_text)
                    if positive_rate_match:
                        positive_rate = positive_rate_match.group(1)
                row['positive_rate'] = positive_rate

                # 27. 差评率
                negative_rate = ""
                if e_ratings and len(e_ratings) >= 5:
                    negative_rate_text = e_ratings[3].text + e_ratings[4].text
                    negative_rate_match = re.search(r'(\d+)%', negative_rate_text)
                    if negative_rate_match:
                        negative_rate = negative_rate_match.group(1)
                row['negative_rate'] = negative_rate

                # 28. 广告花费指数
                row['ad_spend_index'] = "无法从页面直接获取"

                # 29. 广告花费指数更新时间
                row['ad_spend_update_time'] = "无法从页面直接获取"

                # 30. 近7日出现库存紧张
                stock_shortage = False
                e_stock_warning = self.page('css=#availability .a-color-price:contains("Only")')
                if e_stock_warning:
                    stock_shortage = True
                row['recent_stock_shortage'] = stock_shortage

                # 31. 销售状态
                sales_status = "可售"
                e_unavailable = self.page('css=#availability .a-color-price:contains("unavailable")')
                if e_unavailable:
                    sales_status = "不可售"
                row['sales_status'] = sales_status

                # 32. 出现断货
                out_of_stock = False
                e_out_of_stock = self.page('css=#availability:contains("out of stock")')
                if e_out_of_stock:
                    out_of_stock = True
                row['out_of_stock'] = out_of_stock

                # 33. 月度留rating率
                row['monthly_rating_retention'] = "无法从页面直接获取"

                # 34. 月度好评rating率
                row['monthly_positive_rating'] = "无法从页面直接获取"

                # 35. 大类名称
                main_category = ""
                if e_breadcrumbs and len(e_breadcrumbs) > 0:
                    main_category = e_breadcrumbs[0].text.strip()
                row['main_category'] = main_category

                # 36. 大类排名
                main_category_rank = category1_rank  # 复用前面获取的类目1排名
                row['main_category_rank'] = main_category_rank

                # 37. 平均大类排名
                row['avg_main_category_rank'] = "无法从页面直接获取"

                # 38. 近7天排名变化
                row['rank_change_7d'] = "无法从页面直接获取"

                # 39. 近7天排名变化率
                row['rank_change_rate_7d'] = "无法从页面直接获取"

                # 40.细分类目排名
                subcategory_rank = category2_rank  # 复用前面获取的类目2排名
                row['subcategory_rank'] = subcategory_rank

                # 41. 细分类目排名变化
                row['subcategory_rank_change'] = "无法从页面直接获取"

                # 42. Listing完整度
                row['listing_completeness'] = "无法从页面直接获取"

                # 43. 评价数量
                review_count = "0"
                e_review_count = self.page('css=#acrCustomerReviewText')
                if e_review_count:
                    review_match = re.search(r'([\d,]+)', e_review_count.text)
                    if review_match:
                        review_count = review_match.group(1).replace(',', '')
                row['review_count'] = review_count

                # 44.低评高销量系数
                row['low_rating_high_sales_coefficient'] = "无法从页面直接获取"

                # 45. 评价数量增幅（1个月）
                row['review_count_increase_1m'] = "无法从页面直接获取"

                # 46. QA数量
                qa_count = "0"
                e_qa_count = self.page('css=#askATFLink .a-size-base')
                if e_qa_count:
                    qa_match = re.search(r'(\d+)', e_qa_count.text)
                    if qa_match:
                        qa_count = qa_match.group(1)
                row['qa_count'] = qa_count

                # 47. 评分星级
                rating = ""
                e_rating = self.page('css=#acrPopover .a-icon-alt')
                if e_rating:
                    rating_match = re.search(r'([\d\.]+)', e_rating.text)
                    if rating_match:
                        rating = rating_match.group(1)
                row['rating'] = rating

                # 48. 物流方式
                shipping_method = ""
                e_shipping = self.page('css=#mir-layout-DELIVERY_BLOCK span:contains("Fulfilled by Amazon")')
                if e_shipping:
                    shipping_method = "FBA"
                else:
                    e_fbm = self.page('css=#mir-layout-DELIVERY_BLOCK span:contains("by")')
                    if e_fbm:
                        shipping_method = "FBM"
                row['shipping_method'] = shipping_method

                # 49. FBA费用
                row['fba_fee'] = "无法从页面直接获取"

                # 50. 跟卖数量
                seller_count = "0"
                e_seller_count = self.page('css=#olp_feature_div a')
                if e_seller_count:
                    seller_match = re.search(r'(\d+) new', e_seller_count.text)
                    if seller_match:
                        seller_count = seller_match.group(1)
                row['seller_count'] = seller_count

                # 51. 变体数量
                e_totalvariationcount_list = self.page.eles('c=[data-totalvariationcount]') or []
                if e_totalvariationcount_list:
                    totalvariationcounts = e_totalvariationcount_list.get.attrs('data-totalvariationcount')
                    variant_count = 1
                    for totalvariationcount in totalvariationcounts:
                        variant_count *= int(totalvariationcount)
                row['variant_count'] = variant_count

                # 52. 是否做品牌旗舰店
                has_brand_store = False
                e_brand_store = self.page('css=#bylineInfo[href*="stores/page"]')
                if e_brand_store:
                    has_brand_store = True
                row['has_brand_store'] = has_brand_store

                # 53. 体积
                volume = ""
                e_dimensions = self.page('css=#productDetails_detailBullets_sections1 th:contains("Product Dimensions")+td, #detailBullets_feature_div li:contains("Dimensions")')
                if e_dimensions:
                    dimension_text = e_dimensions.text
                    dimension_match = re.search(r'([\d\.x\s]+inches)', dimension_text)
                    if dimension_match:
                        dimensions = dimension_match.group(1).strip()
                        try:
                            dim_parts = re.findall(r'([\d\.]+)', dimensions)
                            if len(dim_parts) >= 3:
                                length, width, height = float(dim_parts[0]), float(dim_parts[1]), float(dim_parts[2])
                                volume = str(length * width * height)
                        except:
                            pass
                row['volume'] = volume

                # 54. 重量
                weight = ""
                e_weight = self.page('css=#productDetails_detailBullets_sections1 th:contains("Weight")+td, #detailBullets_feature_div li:contains("Weight")')
                if e_weight:
                    weight_text = e_weight.text
                    weight_match = re.search(r'([\d\.]+)\s*(ounces|pounds)', weight_text)
                    if weight_match:
                        weight_value = float(weight_match.group(1))
                        weight_unit = weight_match.group(2)
                        if weight_unit == 'pounds':
                            weight = str(weight_value * 16)  # 转换为盎司
                        else:
                            weight = str(weight_value)
                row['weight'] = weight

                # 55. 尺寸标准
                size_standard = ""
                if weight and volume:
                    try:
                        weight_value = float(weight)
                        volume_value = float(volume)

                        if weight_value <= 12 and volume_value <= 180:
                            size_standard = '小尺寸标准'
                        elif weight_value <= 20 and volume_value <= 432:
                            size_standard = '大尺寸标准I'
                        elif weight_value <= 70 and volume_value <= 4320:
                            size_standard = '大尺寸标准II'
                        elif weight_value <= 150 and volume_value <= 13500:
                            size_standard = '大尺寸标准III'
                        else:
                            size_standard = '超大尺寸'
                    except:
                        pass
                row['size_standard'] = size_standard

                # 56. 尺寸
                dimensions = ""
                if e_dimensions:
                    dimension_match = re.search(r'([\d\.x\s]+inches)', e_dimensions.text)
                    if dimension_match:
                        dimensions = dimension_match.group(1).strip()
                row['dimensions'] = dimensions

                # 57. 图片
                images = []
                e_main_image = self.page('css=#landingImage')
                if e_main_image:
                    main_image_url = e_main_image.attr('src')
                    if main_image_url:
                        images.append(main_image_url)

                e_variant_images = self.page.eles('css=#altImages .imageThumbnail img') or []
                for img in e_variant_images:
                    img_url = img.attr('src')
                    if img_url:
                        # 转换缩略图URL为高分辨率URL
                        full_img_url = img_url.replace('._SS40_', '._SL1500_')
                        images.append(full_img_url)

                row['images'] = images

                return row

            get_data(self)
            row.update({
                'site': site,
                'asin': asin,
                'url': url,
            })
            datas.append(row)

        return datas


class AmazonGP(AmazonSessionPage):
    def __init__(self, port=None, proxy_type=1):
        super().__init__(port, proxy_type)

        self.headers_page = {
            "accept": "text/html, application/json",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "cache-control": "no-cache",
            "content-type": "application/json",
            "origin": "https://www.amazon.com",
            "referer": "https://www.amazon.com/gp/bestsellers/?ref_=nav_cs_bestsellers",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 Edg/133.0.0.0",
            "viewport-width": "1912",
            "x-amz-acp-params": "tok=1C2EP4k39QWsW8JmmjkWv2_7NSskGILFxbrXMKsUqxc;ts=1741180651564;rid=BMNW4QPTP45M4R5YDQG7;d1=218;d2=0;tpm=CGHDB.content-id;ref=bs_c_fashion",
            "x-amz-amabot-click-attributes": "disable",
            "x-requested-with": "XMLHttpRequest"
        }

    def get_bestsellers(self, index_url=None):
        json_data = {}
        url_index = index_url or 'https://www.amazon.de/gp/bestsellers'
        self.goto(url_index)
        divs = self.page.eles('c=[data-acp-tracking]')
        html_index = self.page.html

        url_base = "https://www.amazon.de/acp/p13n-zg-list-carousel-desktop/p13n-zg-list-carousel-desktop-4e56ff60-5aa2-4ffb-862d-a826d4d26f00-migration-1738355155172/getCarouselItems"
        par = tqdm(desc="采集asin进度", unit='个')
        for div in divs:
            h2 = div('t=h2').text
            h2 = ' '.join(h2.split(' ')[3:])
            if h2 not in json_data:
                json_data[h2] = {}
            asin_divs = div.eles('c=[data-asin]')
            for asin_div in asin_divs:
                asin = asin_div.attr('data-asin')
                href = asin_div('t=a').attr('href')
                img_url = asin_div('t=img').attr('src')
                json_data[h2][asin] = {
                    'asin': asin,
                    'href': href,
                    'img_url': img_url,
                }
            par.update(len(asin_divs))
            params_json_str = div.attr('data-acp-tracking')
            params = json.loads(params_json_str)
            div_box = div('c=[data-a-carousel-options]')
            attrs = div_box.attrs

            aAjaxStrategy = attrs['data-a-ajax-strategy']
            aCarouselOptions = attrs['data-a-carousel-options']
            aDisplayStrategy = attrs['data-a-display-strategy']
            aTransitionStrategy = attrs['data-a-transition-strategy']
            adOffset = 0
            amabotslotname = attrs['data-amabotslotname']
            devicetype = attrs['data-devicetype']
            faceoutkataname = attrs['data-faceoutkataname']
            faceoutspecs = attrs['data-faceoutspecs']

            individuals = attrs['data-individuals']
            language = attrs['data-language']
            linkparameters = attrs['data-linkparameters']
            marketplaceid = attrs['data-marketplaceid']
            name = attrs['data-name']
            offset = attrs['data-offset']
            pagetype = attrs['data-pagetype']
            reftagprefix = attrs['data-reftagprefix']

            aCarouselOptions_json = json.loads(aCarouselOptions)
            id_list = aCarouselOptions_json['ajax']['id_list']
            ids = id_list[int(offset):]
            indexes = [int(offset) + i for i in range(len(ids))]

            data = {
                'aAjaxStrategy': aAjaxStrategy,
                'aCarouselOptions': aCarouselOptions,
                'aDisplayStrategy': aDisplayStrategy,
                'aTransitionStrategy': aTransitionStrategy,
                'adOffset': adOffset,
                'amabotslotname': amabotslotname,
                'devicetype': devicetype,
                'faceoutkataname': faceoutkataname,
                'faceoutspecs': faceoutspecs,
                'individuals': individuals,
                'language': language,
                'linkparameters': linkparameters,
                'marketplaceid': marketplaceid,
                'name': name,
                'offset': offset,
                'pagetype': pagetype,
                'reftagprefix': reftagprefix,
                'ids': ids,
                'indexes': indexes
            }
            self.page.post(url_base, params=params, json=data, headers=self.headers_page)
            # print(self.page.html)
            if self.page.html:
                lis = self.page.eles('c=li.a-carousel-card-fragment')
                for li in lis:
                    li_box = li('c=[data-asin]')
                    if not li_box:
                        print('li_box not found')
                        continue
                    asin = li_box.attr('data-asin')
                    href = li('t=a').attr('href')
                    img_url = li('t=img').attr('src')
                    json_data[h2][asin] = {
                        'asin': asin,
                        'href': href,
                        'img_url': img_url,
                    }
                par.update(len(lis))
            # print(f'当前asin数量为：{sum([len(v) for k, v in json_data.items()])}')
        par.close()
        return json_data


if __name__ == "__main__":
    # amz = AmazonGP(proxy_type=0, port=9777)
    # json_data = amz.get_bestsellers()
    # amz = AmazonListing(proxy_type=0, port=9777)
    # result_data = amz.get_listing_detail('B0CYC9F97P', 'us')
    # exit()
    pages = 1
    # amazon = AmazonSK(proxy_type=1, port=9223)
    # url = 'https://www.amazon.de/s?k=iphone 16 pro hülle stitch'
    # rs = amazon.get_asins_loc_index(url, pages)
    # exit()
    # insert_or_update_amazon_asin_loc_index(rs, params={})
    # url = 'https://www.amazon.com/s?k=astronaut+iphone+7+case'
    # rs = amazon.get_asins_loc_index(url, pages)
    #
    amazon = AmazonSK(proxy_type=1, port=9444)
    url = 'https://www.amazon.de/s?fs=true&rh=n%3A2867661031'
    rs = amazon.get_asins_by_url_pages(url, pages)
    # url = "https://www.amazon.com/s?k=genshin+impact+phone+case&page=21"
    # rs = amazon.get_asins_by_url_pages(url, pages)

    # amazon = AmazonListing(proxy_type=1, port=9444)
    # content = f'https://www.amazon.de/dp/B0CWH5RNRK?th=1'
    # # content = f'https://www.amazon.com/dp/B0014C0LUC?th=1'
    # rs = amazon.fetch_search_image_price
    # content = f'https://www.amazon.de/dp/B0DFLNM4H7?th=1'
    # rs = amazon.check_asin_listing('B0DFLNM4H7', 'de')

    # asins = 'B0CP76WS8T、B0CP24K3Y6、B0CNXD75RQ、B0CNVT9JGR、B0CNSJL59C、B0CNSLDSL4、B0CNSKNB76'
    # site = 'de'
    # rs = amazon.check_asin_listing(asins, site)

    print(amazon.cur_info)
    print(f'平台数量：{amazon.cur_info["total"]},抓取数量：{len(rs)}')
    print(amazon.cur_info)
    # insert_or_update_amazon_search_data_by_image(rs, params={})
