# -*- coding:UTF-8 -*-
# @FileName  :AmazonDP.py
# @Time      :2024/6/5 14:28
# <AUTHOR>
import json
import re
import time
import amazoncaptcha
from DrissionPage import SessionPage
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse, quote

from DrissionPage._pages.chromium_page import ChromiumPage

COUNTRY_SITE = {
    'uk': 'co.uk',
    'co.uk': 'co.uk',
    'es': 'es',
    'it': 'it',
    'fr': 'fr',
    'de': 'de',
    'com': 'com',  # 以数组形式存储多个匹配值
    '德国': 'de',
    '美国': 'com',
    '英国': 'co.uk',
    '法国': 'fr',
    '意大利': 'it',
    '西班牙': 'es',
}


def replace_page_param(url, new_page_value):
    parsed_url = urlparse(url)
    query_params = parse_qs(parsed_url.query)
    query_params['page'] = [new_page_value]
    # 重新编码查询参数
    encoded_query = urlencode(query_params, doseq=True)
    # 构建新的URL
    new_url = urlunparse((parsed_url.scheme, parsed_url.netloc, parsed_url.path, parsed_url.params, encoded_query, parsed_url.fragment))

    return new_url


def check_site(site):
    site = site.strip()
    if site in COUNTRY_SITE:
        return COUNTRY_SITE[site]
    else:
        return None


class AmazonDPS:
    def __init__(self):
        self.cookie_file = 'amz_cookies.json'
        self.page = SessionPage()
        self.page.timeout = 20
        self.page.session.headers.update({
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
        })
        self.search_headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'origin': 'https://www.amazon.de',
            'pragma': 'no-cache',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'viewport-width': '1912',
        }

        self.zip_codedict = {
            'co.uk': ['W1S 3PR', 'W1S 3'],
            'es': '28035',
            'it': '00185',
            'fr': '75020',
            'de': '14199',
            'co.jp': '163-8001',
            'com': ['90001', '10001']  # 以数组形式存储多个匹配值
        }
        self.data_codedict = {
            'co.uk': 'uk',
            'es': 'es',
            'it': 'it',
            'fr': 'fr',
            'de': 'de',
            'com': 'com'  # 以数组形式存储多个匹配值
        }
        self.cur_country = None
        self.cur_address = None

    def check_page(self):
        for i1 in range(3):  # 验证码最大重试3次
            cur_url = self.page.url
            domain = urlparse(cur_url).netloc

            if 'id="captchacharacters"' in self.page.html:  # 验证码
                print('检测到验证码页面...', end=' ')
                img_url = self.page('c=[class="a-row a-text-center"] img').attr('src')
                # print(img_url)
                print(f'验证码第{i1 + 1}次识别中...', end=' ')
                captcha_result = amazoncaptcha.AmazonCaptcha.fromlink(img_url).solve()
                print('本次识别结果为：', captcha_result)

                amzn = self.page('c=input[name="amzn"]').attr('value')
                amznr = self.page('c=input[name="amzn-r"]').attr('value')
                # print(amzn, amznr)
                params = {
                    "amzn": amzn,
                    "amzn-r": amznr,
                    "field-keywords": captcha_result
                }
                self.page.get(f"https://{domain}/errors/validateCaptcha", params=params)
                continue

            while 'ref=cs_503_link' in self.page.html:
                print('503页面')
                time.sleep(2)
                e_503 = self.page('@href:ref=cs_503_link', timeout=2)
                # e_503 = self.page('t:a', index=-1, timeout=2)
                # if e_503:
                #     e_503.click()
                self.page.get(cur_url)

            if 'ref=cs_404_link' in self.page.html:
                return False

            return True  # 已检测到可能的异常全处理完则跳出页面页面检测
        else:
            print('验证码识别失败')
            raise Exception("验证码识别失败")

    def change_address(self):
        url = self.page.url
        html = self.page.html
        if 'glow-ingress-line2' not in html:
            print('当前页面未检测到地址标签！')
            return
        address = self.page('#glow-ingress-line2')
        addressText = address.text.strip()
        domain = urlparse(url).netloc
        matchFound = False
        zipcode = '90001'
        domain_code = domain.split("amazon.")[1]
        value = self.zip_codedict[domain_code]
        self.cur_country = self.data_codedict[domain_code]
        self.cur_address = addressText
        print(f'当前站点：{self.cur_country},当前地址为：{addressText}')

        if isinstance(value, list):  # 判断是否为列表（Python 中的数组）
            if not any(item in addressText for item in value):  # 如果值是列表，检查列表中的任何元素是否包含在 addressText 中
                matchFound = True
                zipcode = value[0]
        else:  # 如果值不是列表
            if value not in addressText:  # 直接比较值是否在 addressText 中
                zipcode = value
                matchFound = True

        if matchFound:
            print(f'当前站点：{self.cur_country},当前地址为：{addressText},与目标地址:{zipcode}不匹配。正在修改地址信息...', end=' ')
            # 获取数据
            get_data = self.page("#nav-global-location-data-modal-action").attr('data-a-modal')
            modal_data = json.loads(get_data)
            url2 = modal_data["url"]
            anti_csrftoken_a2z = modal_data['ajaxHeaders']["anti-csrftoken-a2z"]

            headers = {
                'accept': 'text/html,*/*',
                'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
                'anti-csrftoken-a2z': anti_csrftoken_a2z,
                'cache-control': 'no-cache',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                'viewport-width': '1912',
                'x-requested-with': 'XMLHttpRequest',
            }
            base_url = f"https://{domain}{url2}"
            self.page.get(base_url, verify=False, headers=headers)
            # response = self.check_captcha(response)

            script_content = self.page("c=div script")
            if script_content and "CSRF_TOKEN" in script_content.text:
                csrf_token_match = re.search(r"CSRF_TOKEN\s*:\s*\"([^\"]+)\"", script_content)
                if csrf_token_match:
                    csrf_token = csrf_token_match.group(1)
                    print("匹配到安全sign", end=' ')
                    page_type_match = re.search(r"pageType=([^&]+)", base_url)
                    if page_type_match:
                        page_type = page_type_match.group(1)
                        pageurl = base_url
                        storecontext = "generic"
                        if "/dp" in pageurl:
                            storecontext = "wireless"
                        self.change_zipcode(zipcode, storecontext, page_type, csrf_token, domain)
                        self.page.get(url)
                        # response = self.check_captcha(response)
                        if self.page.response.status_code == 200:
                            self.cur_address = self.page('#glow-ingress-line2').text.strip()
                            print(f"站点{self.cur_country}的地址已切换为：{self.cur_address}")
                            with open(self.cookie_file, "w") as f:
                                f.write(json.dumps(self.page.cookies().as_dict()))
            else:
                print('页面信息不完整，可能是TLS指纹被识别！')

    def change_zipcode(self, zipcode: str, storecontext: str, page_type: str, csrf_token: str, domain: str):
        url = f"https://{domain}/portal-migration/hz/glow/address-change?actionSource=glow"

        data = {
            "locationType": "LOCATION_INPUT",
            "zipCode": zipcode,
            "storeContext": storecontext,
            "deviceType": "web",
            "pageType": page_type,
            "actionSource": "glow"
        }

        headers = {
            "accept": "text/html,*/*",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "anti-csrftoken-a2z": csrf_token,
            "content-type": "application/json",
            "x-requested-with": "XMLHttpRequest"
        }

        response = self.session.post(url, headers=headers, data=json.dumps(data), allow_redirects=True)
        # result = response.text
        # print(f'result:{result}')  # 根据需要处理返回结果
        pass

    def download_img(self, img_url, file_name):
        self.page.get(img_url)
        if self.page.response.status_code == 200:
            with open(file_name, 'wb') as f:
                f.write(self.page.raw_data)
            return True
        else:
            print(f'{img_url}下载失败')
            return False

    def get_search_image(self, content, site):
        # 是否二进制类型
        if not isinstance(content, bytes):
            with open(content, 'rb') as f:
                img_data = f.read()
        else:
            img_data = content

        params = {'stylesnapToken': ''}
        files = {'explore-looks.jpg': ('explore-looks.jpg', img_data, 'image/jpeg')}

        # seatch_img = 'https://www.amazon.de/stylesnap?pd_rd_w=FX2kO&content-id=amzn1.sym.d26e24db-d6a0-41ff-bb8a-bf1969aea086:amzn1.sym.d26e24db-d6a0-41ff-bb8a-bf1969aea086&pf_rd_p=d26e24db-d6a0-41ff-bb8a-bf1969aea086&pf_rd_r=XCYKR94DEP60T0QK6BA4&pd_rd_wg=ELz3t&pd_rd_r=24526b89-2149-41f7-9b3f-0c4cbacb50b4&qid=1717315709&ref_=sxts_snpl_1_1_d26e24db-d6a0-41ff-bb8a-bf1969aea086'
        # seatch_img = 'https://www.amazon.com/shopthelook?q=local'
        seatch_img = f'https://www.amazon.{site}/stylesnap?q=local'
        self.search_headers['origin'] = f'https://www.amazon.{site}'
        datas = None
        try_count = 8
        for i in range(try_count):
            self.page.get(seatch_img)
            self.check_page()
            stylesnap = self.page('@name=stylesnap').attr('value')
            if not stylesnap:
                print('未获取到stylesnap')
                return None
            stylesnapToken = quote(stylesnap)
            params['stylesnapToken'] = stylesnapToken

            self.page.post(
                    f'https://www.amazon.{site}/stylesnap/upload',
                    params=params,
                    headers=self.search_headers,
                    timeout=20,
                    files=files)
            self.check_page()
            if 'searchResults' in self.page.html:
                datas = self.page.json
                break
            print(f'\r{i + 1} / {try_count}次搜索失败，3秒后重新搜索...', end='')
            time.sleep(3)
        return datas

    def get_dp_all_image(self, url):
        self.page.get(url)
        if not self.check_page():
            return None
        # all_pics = re.findall(',"large":"(.*?)"', self.page.html)  # 大图
        div = self.page('tag=script@@text():ImageBlockATF')
        if div:
            all_pics = re.findall('"hiRes":"(.*?)"', div.inner_html)  # 高分辨率
        else:
            all_pics = re.findall('"hiRes":"(.*?)"', self.page.html)  # 高分辨率
            print(f'指定区域未匹配到图片，当前搜索全页面图片共{len(all_pics)}张', url) if all_pics else ''
        all_pics = list(set(all_pics))
        # print(all_pics)
        return all_pics


if __name__ == "__main__":
    dps = AmazonDPS()
    # img_path = rf'D:\Pictures\71eLH8gN80L._AC_UL320_.jpg'
    # datas = dps.get_search_image(img_path, 'de')
    url = 'https://www.amazon.de/dp/B0CL9SDB5P/ref=mweb_up_am_fl_st_na_un_up_sm_web'
    # url = 'https://www.amazon.de/dp/B0BQNDXPKY/ref=mweb_up_am_fl_st_na_un_up_sm_web'
    imgs = dps.get_dp_all_image(url)
    print(len(imgs), imgs)
