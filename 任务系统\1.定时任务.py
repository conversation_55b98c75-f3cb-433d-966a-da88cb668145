# -*- coding:UTF-8 -*-
# @FileName  :1.定时任务.py
# @Time      :2024/12/23 11:56
# <AUTHOR>
# status_updater.py
from utils_mrc.pub_fun import *
from utils_mrc.MysqlHelper import *
import logging
import schedule

# 配置日志
logging.basicConfig(level=logging.INFO, format='[%(asctime)s %(levelname)s]: %(message)s')


def update_fetch_report_status():
    tb_name = "rpa.`data_lingxing_report_generate`"
    sql_sel = f"select id from {tb_name} where fetch_status <> 1 and datetime <> UNIX_TIMESTAMP(CURRENT_DATE)"
    rs = MS100.get_dict(sql_sel)
    if not rs:
        logging.info('此时间段无需要处理的报表任务！')
        return
    ids = [i['id'] for i in rs]
    sql = f"""
    update {tb_name} set fetch_status=1 , datetime=UNIX_TIMESTAMP(CURRENT_DATE) where id in %s
    """
    rs = MS100.update(sql, (ids,))
    if rs:
        logging.info(f'<{tb_name}> 重置{rs}条报表任务数据！')


schedule.every().day.at("03:00").do(update_fetch_report_status)  # 每天*点执行一次任务

if __name__ == "__main__":
    logging.info(f'开始运行，正在初始化程序{get_cur_run_file()}...')
    while True:
        schedule.run_pending()
        time.sleep(1)
