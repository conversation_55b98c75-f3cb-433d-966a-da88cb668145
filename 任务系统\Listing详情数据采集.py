# -*- coding:UTF-8 -*-
# @FileName  :main.py
# @Time      :2024/6/18 11:27
# <AUTHOR>
from utils_mrc.pub_fun import *
from work.自动化.亚马逊.Amazon import *


def main(amazon):
    # 获取主任务
    main_tasks = MS.get_dict(
            f'SELECT id, app_id, asins, user_id, username, datetime, site  FROM `task_listing`  WHERE STATUS = 1 ORDER BY priority desc, id asc'
    )
    if not main_tasks:
        time.sleep(10)
        return
    for task in main_tasks:
        task_id = task.get('id')
        # 修改所关联任务状态为执行中
        if MS.update('UPDATE `task_listing` SET status = 2,`execute_time` = %s WHERE id = %s and `status` = 1', (now_int(), task_id)) < 1:
            logging(f'当前任务已经在执行')
            return False
        app_id = task.get('app_id')
        asins_str = task.get('asins')
        user_id = task.get('user_id')
        username = task.get('username')
        task_time = task.get('task_time')  # 关联任务的所属时间`datetime`
        site = task.get('site')
        params = {  # 更新任务表/数据表记录的字段
            'task_id': task_id,
            'app_id': app_id,
            'user_id': user_id,
            'username': username,
            'task_time': task_time,
            'date_time': get_today_zero_timestamp(),  # 当天0点时间戳
            'site': site,
        }
        # 开始抓取任务
        if app_id == 12:
            task_goods_id = task.get('asins', 0)
            sql_sel = 'select asin from data_sprite_asin where task_id in (SELECT id FROM `task_goods` where pid =%s and app_id = 3)'
            result_data = MS.get_dict(sql_sel, (task_goods_id))
            asin_list = [i.get('asin') for i in result_data]
        else:
            asin_list = split_get_list(asins_str)
        logging(f'当前应用ID:{app_id},任务ID:{task_id},asin长度:{len(asin_list)}')
        amazon.task_params = params
        result_data = amazon.check_asin_listing(asin_list, site, app_id)  # 抓取数据

        task_num = len(result_data)  # 任务数量
        data_status = 1 if task_num > 0 else 3  # 数据状态
        params['create_time'] = now_int()
        params['platform_num'] = task_num
        params['task_num'] = task_num
        params['data_status'] = data_status
        params['done_time'] = now_int()
        params['status'] = amazon.cur_info['status']
        params['result'] = '成功' if amazon.cur_info['status'] == 10 else amazon.cur_info['result']
        insert_amazon_listing(result_data, params)  # 更新数据表

        logging(f'任务:{task_id} 已执行结束')


if __name__ == '__main__':
    logging(f'开始运行，正在初始化程序{get_cur_run_file()}...')
    # amazon = AmazonDP()
    amazon = AmazonListing(proxy_type=2, port=9555)
    while True:
        try:
            main(amazon)
        except Exception as e:
            err = traceback.format_exc()
            print(err)
            msg = '主程序抓取异常！'
            logging(msg)
            fsmsg.send('Listing页面检查', msg, err)
