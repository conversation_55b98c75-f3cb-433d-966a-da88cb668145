-- 清理货件签收差异表中的重复数据
-- 保留每组重复数据中ID最大的那条（最新的数据）

USE rpa;

-- 1. 查看重复数据统计
SELECT 
    shipment_id, 
    msku, 
    asin, 
    COUNT(*) as duplicate_count,
    MIN(id) as min_id,
    MAX(id) as max_id
FROM data_lingxing_shipment_tracking 
GROUP BY shipment_id, msku, asin 
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC
LIMIT 10;

-- 2. 查看总的重复记录数
SELECT 
    COUNT(*) as total_records,
    COUNT(DISTINCT CONCAT(shipment_id, '-', msku, '-', asin)) as unique_combinations,
    COUNT(*) - COUNT(DISTINCT CONCAT(shipment_id, '-', msku, '-', asin)) as duplicate_records
FROM data_lingxing_shipment_tracking;

-- 3. 删除重复数据，保留ID最大的记录（最新的）
-- 使用临时表方法避免MySQL的限制
CREATE TEMPORARY TABLE temp_keep_ids AS
SELECT MAX(id) as keep_id
FROM data_lingxing_shipment_tracking
GROUP BY shipment_id, msku, asin;

-- 删除不在保留列表中的记录
DELETE FROM data_lingxing_shipment_tracking 
WHERE id NOT IN (SELECT keep_id FROM temp_keep_ids);

-- 4. 验证清理结果
SELECT 
    COUNT(*) as total_records_after_cleanup,
    COUNT(DISTINCT CONCAT(shipment_id, '-', msku, '-', asin)) as unique_combinations_after
FROM data_lingxing_shipment_tracking;

-- 5. 添加唯一索引
ALTER TABLE data_lingxing_shipment_tracking 
ADD UNIQUE INDEX uk_shipment_msku_asin (shipment_id, msku, asin);

-- 6. 查看最终的索引结构
SHOW INDEX FROM data_lingxing_shipment_tracking;
