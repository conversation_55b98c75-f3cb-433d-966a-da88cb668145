方法1：PowerShell永久更改执行策略 （需要管理员模式运行）:

Set-ExecutionPolicy -Scope LocalMachine -ExecutionPolicy RemoteSigned
    
	
方法2：
修改注册表永久更改执行策略
虽然可以通过 PowerShell 命令更改执行策略，但如果你想通过修改注册表来永久更改执行策略，可以按照以下步骤操作：
步骤
打开注册表编辑器：
按 Win + R 打开运行对话框，输入 regedit，然后按 Enter。
导航到相关注册表项：
在注册表编辑器中，导航到以下路径：
     HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\PowerShell\1\ShellIds\Microsoft.PowerShell
创建或修改 ExecutionPolicy 值：
在右侧窗格中，查找名为 ExecutionPolicy 的字符串值。如果没有找到，可以右键点击右侧窗格，选择 新建 -> 字符串值，并命名为 ExecutionPolicy。
双击 ExecutionPolicy，在弹出的对话框中输入你希望设置的执行策略值，例如：
Restricted：不允许任何脚本运行。
AllSigned：只允许已签名的脚本运行。
RemoteSigned：允许本地脚本无签名运行，但远程脚本必须签名。
Unrestricted：允许所有脚本运行，但会提示用户确认未签名的脚本。
Bypass：不执行任何策略检查，允许所有脚本运行，不提示用户。
保存并退出：
点击 确定 保存设置，然后关闭注册表编辑器。
重启 PowerShell：
为了使更改生效，建议重启 PowerShell 或重新启动计算机。