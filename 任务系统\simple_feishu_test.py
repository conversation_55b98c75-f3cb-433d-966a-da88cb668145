#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的飞书接口测试脚本
用于快速诊断飞书接口问题
"""

import requests
import json
from datetime import datetime

# 飞书配置
APP_ID = "cli_a8e6d2538d3b9013"
APP_SECRET = "VCwxp8nuTP684G9qCIdXbpyRyztLZGDg"
APP_TOKEN = "VNmZbpO8Yax8wus0hNXc6hH7nYg"
TABLE_ID = "tblsLt4LrqByhauC"  # 货件签收差异表格


def get_token():
    """获取访问令牌"""
    print("1. 获取访问令牌...")
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    headers = {"Content-Type": "application/json"}
    data = {"app_id": APP_ID, "app_secret": APP_SECRET}
    
    try:
        response = requests.post(url, headers=headers, json=data)
        result = response.json()
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200 and result.get("code") == 0:
            token = result.get("tenant_access_token")
            print(f"   ✅ 成功获取token: {token[:20]}...")
            return token
        else:
            print(f"   ❌ 获取token失败: {result.get('msg', '未知错误')}")
            return None
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return None


def test_table_info(token):
    """测试获取表格信息"""
    print("\n2. 测试获取表格信息...")
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{TABLE_ID}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        result = response.json()
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            print("   ✅ 表格信息获取成功")
            return True
        else:
            print(f"   ❌ 表格信息获取失败: {result.get('msg', '未知错误')}")
            return False
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False


def test_get_fields(token):
    """测试获取表格字段信息"""
    print("\n3. 测试获取表格字段信息...")
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{TABLE_ID}/fields"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        result = response.json()
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            fields = result.get("data", {}).get("items", [])
            print(f"   ✅ 成功获取 {len(fields)} 个字段:")
            for field in fields[:10]:  # 只显示前10个字段
                field_name = field.get("field_name", "")
                field_type = field.get("type", "")
                print(f"      - {field_name} ({field_type})")
            if len(fields) > 10:
                print(f"      ... 还有 {len(fields) - 10} 个字段")
            return True
        else:
            print(f"   ❌ 获取字段失败: {result.get('msg', '未知错误')}")
            print(f"   完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return False
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False


def test_insert_record(token):
    """测试插入单条记录"""
    print("\n4. 测试插入单条记录...")
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{TABLE_ID}/records"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 简单的测试数据
    timestamp = int(datetime.now().timestamp())
    test_data = {
        "fields": {
            "MSKU": f"SIMPLE-TEST-{timestamp}",
            "FNSKU": "X00SIMPLE001",
            "ASIN": "B0SIMPLE001",
            "货件编号": f"FBA15SIMPLE{timestamp}",
            "货件名称": "简单测试货件",
            "店铺": "简单测试店铺",
            "国家": "简单测试国家",
            "申报量": 10,
            "已发货量": 10,
            "签收量": 9,
            "状态": "测试中"
        }
    }
    
    try:
        response = requests.post(url, headers=headers, json=test_data)
        result = response.json()
        
        print(f"   状态码: {response.status_code}")
        print(f"   请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            print("   ✅ 记录插入成功")
            return True
        else:
            print(f"   ❌ 记录插入失败: {result.get('msg', '未知错误')}")
            return False
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False


def test_get_records(token):
    """测试获取记录"""
    print("\n5. 测试获取记录...")
    url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{TABLE_ID}/records"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    params = {
        "page_size": 5  # 只获取5条记录
    }
    
    try:
        response = requests.get(url, headers=headers, params=params)
        result = response.json()
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            items = result.get("data", {}).get("items", [])
            print(f"   ✅ 成功获取 {len(items)} 条记录")
            
            for i, item in enumerate(items, 1):
                fields = item.get("fields", {})
                msku = fields.get("MSKU", "N/A")
                shipment_id = fields.get("货件编号", "N/A")
                print(f"      记录{i}: MSKU={msku}, 货件编号={shipment_id}")
            
            return True
        else:
            print(f"   ❌ 获取记录失败: {result.get('msg', '未知错误')}")
            print(f"   完整响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return False
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🔍 飞书接口简单测试")
    print("=" * 50)
    print(f"APP_ID: {APP_ID}")
    print(f"APP_TOKEN: {APP_TOKEN}")
    print(f"TABLE_ID: {TABLE_ID}")
    print("=" * 50)
    
    # 1. 获取token
    token = get_token()
    if not token:
        print("\n❌ 无法获取token，测试终止")
        return
    
    # 2. 测试表格信息
    if not test_table_info(token):
        print("\n❌ 表格访问失败，可能是权限问题")
        return
    
    # 3. 测试字段信息
    test_get_fields(token)
    
    # 4. 测试获取记录
    test_get_records(token)
    
    # 5. 测试插入记录
    test_insert_record(token)
    
    print("\n" + "=" * 50)
    print("✅ 测试完成！")
    print("\n💡 如果插入失败，常见原因:")
    print("   1. 字段名不匹配（检查表格中的实际字段名）")
    print("   2. 数据类型不匹配（数字字段传入了字符串等）")
    print("   3. 必填字段缺失")
    print("   4. 权限不足（需要表格编辑权限）")


if __name__ == '__main__':
    main()
