#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书接口测试脚本
用于测试飞书多维表格的各种操作
"""

import os
import sys
import json
import requests
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from 任务系统.常用.feishu_bitable import (
    get_tenant_access_token,
    push_data_to_feishu_bitable,
    push_data_batch_to_feishu_bitable,
    upsert_data_batch_to_feishu_bitable,
    fetch_all_records,
    TABLE_CONFIGS,
    APP_TOKEN
)


def test_token():
    """测试获取访问令牌"""
    print("=== 测试获取访问令牌 ===")
    try:
        token = get_tenant_access_token()
        if token:
            print(f"✅ 成功获取token: {token[:20]}...")
            return True
        else:
            print("❌ 获取token失败")
            return False
    except Exception as e:
        print(f"❌ 获取token异常: {e}")
        return False


def test_table_access():
    """测试表格访问权限"""
    print("\n=== 测试表格访问权限 ===")
    
    token = get_tenant_access_token()
    if not token:
        print("❌ 无法获取token")
        return False
    
    # 测试货件签收差异表格
    table_id = TABLE_CONFIGS.get("shipment_tracking", "")
    if not table_id:
        print("❌ 未找到货件签收差异表格配置")
        return False
    
    print(f"测试表格ID: {table_id}")
    
    try:
        # 尝试获取表格信息
        api_url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{table_id}"
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(api_url, headers=headers)
        result = response.json()
        
        print(f"HTTP状态码: {response.status_code}")
        print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            print("✅ 表格访问正常")
            return True
        else:
            print(f"❌ 表格访问失败: {result.get('msg', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 表格访问异常: {e}")
        return False


def test_single_insert():
    """测试单条数据插入"""
    print("\n=== 测试单条数据插入 ===")
    
    test_data = {
        "MSKU": f"TEST-MSKU-{int(datetime.now().timestamp())}",
        "FNSKU": "X00TEST001",
        "ASIN": "B0TEST001",
        "货件编号": f"FBA15TEST{int(datetime.now().timestamp())}",
        "货件名称": "测试货件-单条插入",
        "追踪编号": "TEST001",
        "店铺": "测试店铺",
        "国家": "测试国家",
        "申报量": 10,
        "已发货量": 10,
        "签收量": 9,
        "预计可诊断数量": 1,
        "预计可诊断金额": 5.99,
        "剩余可诊断天数": 30,
        "状态": "未诊断",
        "备注": "单条插入测试数据",
        "创建时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "更新时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    
    try:
        result = push_data_to_feishu_bitable(test_data, table_key="shipment_tracking")
        if result:
            print("✅ 单条数据插入成功")
            return True
        else:
            print("❌ 单条数据插入失败")
            return False
    except Exception as e:
        print(f"❌ 单条数据插入异常: {e}")
        return False


def test_batch_insert():
    """测试批量数据插入"""
    print("\n=== 测试批量数据插入 ===")
    
    timestamp = int(datetime.now().timestamp())
    test_data_list = []
    
    for i in range(3):
        test_data = {
            "MSKU": f"TEST-BATCH-MSKU-{timestamp}-{i+1}",
            "FNSKU": f"X00BATCH{i+1:03d}",
            "ASIN": f"B0BATCH{i+1:03d}",
            "货件编号": f"FBA15BATCH{timestamp}{i+1}",
            "货件名称": f"测试货件-批量插入-{i+1}",
            "追踪编号": f"BATCH{i+1:03d}",
            "店铺": "批量测试店铺",
            "国家": "批量测试国家",
            "申报量": 10 + i,
            "已发货量": 10 + i,
            "签收量": 9 + i,
            "预计可诊断数量": 1,
            "预计可诊断金额": 5.99 + i,
            "剩余可诊断天数": 30 - i,
            "状态": "未诊断",
            "备注": f"批量插入测试数据-{i+1}",
            "创建时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "更新时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        test_data_list.append(test_data)
    
    try:
        push_data_batch_to_feishu_bitable(test_data_list, table_key="shipment_tracking")
        print("✅ 批量数据插入完成")
        return True
    except Exception as e:
        print(f"❌ 批量数据插入异常: {e}")
        return False


def test_upsert():
    """测试upsert操作（有则更新，无则插入）"""
    print("\n=== 测试upsert操作 ===")
    
    timestamp = int(datetime.now().timestamp())
    
    # 第一次插入
    test_data_list = [{
        "MSKU": f"TEST-UPSERT-MSKU-{timestamp}",
        "FNSKU": "X00UPSERT001",
        "ASIN": "B0UPSERT001",
        "货件编号": f"FBA15UPSERT{timestamp}",
        "货件名称": "测试货件-UPSERT-初始",
        "追踪编号": "UPSERT001",
        "店铺": "UPSERT测试店铺",
        "国家": "UPSERT测试国家",
        "申报量": 100,
        "已发货量": 100,
        "签收量": 90,
        "预计可诊断数量": 10,
        "预计可诊断金额": 50.00,
        "剩余可诊断天数": 30,
        "状态": "未诊断",
        "备注": "UPSERT测试数据-初始插入",
        "创建时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "更新时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }]
    
    try:
        print("第一次插入...")
        upsert_data_batch_to_feishu_bitable(
            test_data_list, 
            table_key="shipment_tracking",
            key_fields=("货件编号", "MSKU", "ASIN")
        )
        
        # 等待一下
        import time
        time.sleep(2)
        
        # 第二次更新（相同的唯一键，不同的数据）
        test_data_list[0].update({
            "货件名称": "测试货件-UPSERT-更新后",
            "签收量": 95,  # 更新签收量
            "预计可诊断数量": 5,  # 更新预计可诊断数量
            "备注": "UPSERT测试数据-已更新",
            "更新时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
        
        print("第二次更新...")
        upsert_data_batch_to_feishu_bitable(
            test_data_list, 
            table_key="shipment_tracking",
            key_fields=("货件编号", "MSKU", "ASIN")
        )
        
        print("✅ UPSERT操作完成")
        return True
        
    except Exception as e:
        print(f"❌ UPSERT操作异常: {e}")
        return False


def test_fetch_records():
    """测试获取记录"""
    print("\n=== 测试获取记录 ===")
    
    try:
        records = fetch_all_records(
            table_key="shipment_tracking",
            key_fields=("货件编号", "MSKU", "ASIN")
        )
        
        print(f"✅ 成功获取 {len(records)} 条记录")
        
        # 显示前几条记录的键
        count = 0
        for key, record_id in records.items():
            if count >= 5:  # 只显示前5条
                break
            print(f"  键: {key} -> 记录ID: {record_id}")
            count += 1
        
        if len(records) > 5:
            print(f"  ... 还有 {len(records) - 5} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取记录异常: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    # 测试无效数据
    invalid_data = {
        "无效字段": "这个字段在表格中不存在",
        "MSKU": "TEST-ERROR"
    }
    
    try:
        result = push_data_to_feishu_bitable(invalid_data, table_key="shipment_tracking")
        print(f"无效数据插入结果: {result}")
        return True
    except Exception as e:
        print(f"❌ 错误处理测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始飞书接口测试...")
    print(f"APP_TOKEN: {APP_TOKEN}")
    print(f"货件签收差异表格ID: {TABLE_CONFIGS.get('shipment_tracking', 'NOT_FOUND')}")
    print("=" * 50)
    
    tests = [
        ("获取访问令牌", test_token),
        ("表格访问权限", test_table_access),
        ("单条数据插入", test_single_insert),
        ("批量数据插入", test_batch_insert),
        ("UPSERT操作", test_upsert),
        ("获取记录", test_fetch_records),
        ("错误处理", test_error_handling),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} 测试出现未捕获异常: {e}")
            results[test_name] = False
    
    # 输出测试结果汇总
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️  有测试失败，请检查配置和权限")


if __name__ == '__main__':
    main()
