import requests
from datetime import datetime

# 飞书多维表格配置（请根据实际情况修改）
APP_ID = "cli_a8e6d2538d3b9013"
APP_SECRET = "VCwxp8nuTP684G9qCIdXbpyRyztLZGDg"
APP_TOKEN = "VNmZbpO8Yax8wus0hNXc6hH7nYg"
TABLE_ID = "tblhrZNKLU9THzWt"  # 默认表格ID

# 支持多表格配置
TABLE_CONFIGS = {
    "fba_feediff": "tblhrZNKLU9THzWt",  # FBA费差异表格
    "shipment_tracking": "tblsLt4LrqByhauC"  # 货件签收差异表格
}

def get_tenant_access_token():
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    headers = {"Content-Type": "application/json"}
    data = {"app_id": APP_ID, "app_secret": APP_SECRET}
    response = requests.post(url, headers=headers, json=data).json()
    return response.get("tenant_access_token")

def push_data_to_feishu_bitable(data: dict, table_key: str = "fba_feediff"):
    """推送单条数据到飞书多维表格

    Args:
        data: 要推送的数据字典
        table_key: 表格配置键，默认为"fba_feediff"
    """
    token = get_tenant_access_token()
    if not token:
        print("获取token失败")
        return False

    table_id = TABLE_CONFIGS.get(table_key, TABLE_ID)
    api_url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{table_id}/records"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    payload = {
        "fields": data
    }
    response = requests.post(api_url, headers=headers, json=payload)
    print(response.json())
    if response.status_code == 200:
        print("推送飞书成功")
        return True
    else:
        print("推送飞书失败", response.text)
        return False

def push_data_batch_to_feishu_bitable(data_list, table_key: str = "fba_feediff"):
    """
    批量推送到飞书多维表格（每次最多20条）

    Args:
        data_list: List[dict]，每个dict为一条fields数据
        table_key: 表格配置键，默认为"fba_feediff"
    """
    token = get_tenant_access_token()
    if not token:
        print("获取token失败")
        return False

    table_id = TABLE_CONFIGS.get(table_key, TABLE_ID)
    api_url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{table_id}/records/batch_create"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    # 分批，每批最多20条
    for i in range(0, len(data_list), 20):
        batch = data_list[i:i+20]
        payload = {"records": [{"fields": item} for item in batch]}
        response = requests.post(api_url, headers=headers, json=payload)
        print(response.json())
        if response.status_code == 200:
            print(f"批量推送飞书成功（{len(batch)}条）")
        else:
            print("批量推送飞书失败", response.text)

def find_record_id_by_fields(shop_name, country, table_key: str = "fba_feediff"):
    """
    根据店铺和国家查找飞书多维表格中是否已存在记录，返回record_id或None

    Args:
        shop_name: 店铺名称
        country: 国家
        table_key: 表格配置键，默认为"fba_feediff"
    """
    token = get_tenant_access_token()
    table_id = TABLE_CONFIGS.get(table_key, TABLE_ID)
    api_url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{table_id}/records"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    # filter语法：AND(["店铺"="xxx"],["国家"="yyy"])
    params = {
        "filter": f'AND([\"店铺\"="{shop_name}"],[\"国家\"="{country}"])',
        "page_size": 1
    }
    resp = requests.get(api_url, headers=headers, params=params)
    data = resp.json()
    if data.get("data", {}).get("items"):
        return data["data"]["items"][0]["record_id"]
    return None

def update_feishu_record(record_id, fields, table_key: str = "fba_feediff"):
    """
    根据record_id更新飞书多维表格记录

    Args:
        record_id: 记录ID
        fields: 要更新的字段数据
        table_key: 表格配置键，默认为"fba_feediff"
    """
    token = get_tenant_access_token()
    table_id = TABLE_CONFIGS.get(table_key, TABLE_ID)
    api_url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{table_id}/records/{record_id}"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    payload = {"fields": fields}
    resp = requests.put(api_url, headers=headers, json=payload)
    print(resp.json())
    return resp.status_code == 200

# 新的有则更新无则新增批量推送

def fetch_all_records(table_key: str = "fba_feediff", key_fields: tuple = ("店铺", "国家")):
    """获取指定表格的所有记录

    Args:
        table_key: 表格配置键，默认为"fba_feediff"
        key_fields: 用作唯一键的字段组合，默认为("店铺", "国家")

    Returns:
        dict: {(key_field1_value, key_field2_value): record_id}
    """
    token = get_tenant_access_token()
    table_id = TABLE_CONFIGS.get(table_key, TABLE_ID)
    api_url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{table_id}/records"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    all_items = []
    page_token = ""
    while True:
        params = {"page_size": 1000}
        if page_token:
            params["page_token"] = page_token
        resp = requests.get(api_url, headers=headers, params=params)
        data = resp.json()
        items = data.get("data", {}).get("items", [])
        all_items.extend(items)
        page_token = data.get("data", {}).get("page_token", "")
        if not page_token:
            break
    # 返回字典：{(key_field1_value, key_field2_value): record_id}
    return {tuple(item['fields'].get(field) for field in key_fields): item['record_id'] for item in all_items}

def split_new_and_update(data_list, existing_map, key_fields: tuple = ("店铺", "国家")):
    """分离新增和更新的数据

    Args:
        data_list: 要处理的数据列表
        existing_map: 现有记录映射
        key_fields: 用作唯一键的字段组合，默认为("店铺", "国家")

    Returns:
        tuple: (to_create, to_update)
    """
    to_create = []
    to_update = []
    for fields in data_list:
        key = tuple(fields.get(field) for field in key_fields)
        if key in existing_map:
            to_update.append({"record_id": existing_map[key], "fields": fields})
        else:
            to_create.append(fields)
    return to_create, to_update

def batch_create_feishu_records(data_list, table_key: str = "fba_feediff"):
    """批量创建飞书记录

    Args:
        data_list: 要创建的数据列表
        table_key: 表格配置键，默认为"fba_feediff"
    """
    token = get_tenant_access_token()
    table_id = TABLE_CONFIGS.get(table_key, TABLE_ID)
    api_url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{table_id}/records/batch_create"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    for i in range(0, len(data_list), 1000):
        batch = data_list[i:i+1000]
        payload = {"records": [{"fields": item} for item in batch]}
        response = requests.post(api_url, headers=headers, json=payload)
        # print(response.json())
        if response.status_code == 200:
            print(f"批量新增飞书成功（{len(batch)}条）")
        else:
            print("批量新增飞书失败", response.text)

def batch_update_feishu_records(update_list, table_key: str = "fba_feediff"):
    """批量更新飞书记录

    Args:
        update_list: 要更新的数据列表
        table_key: 表格配置键，默认为"fba_feediff"
    """
    token = get_tenant_access_token()
    table_id = TABLE_CONFIGS.get(table_key, TABLE_ID)
    api_url = f"https://open.feishu.cn/open-apis/bitable/v1/apps/{APP_TOKEN}/tables/{table_id}/records/batch_update"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    for i in range(0, len(update_list), 1000):
        batch = update_list[i:i+1000]
        payload = {"records": batch}
        response = requests.post(api_url, headers=headers, json=payload)
        # print(response.json())
        if response.status_code == 200:
            print(f"批量更新飞书成功（{len(batch)}条）")
        else:
            print("批量更新飞书失败", response.text)

def upsert_data_batch_to_feishu_bitable(data_list, table_key: str = "fba_feediff", key_fields: tuple = ("店铺", "国家")):
    """
    高效批量upsert：先拉取所有已有记录，分为新增和更新，分别批量处理

    Args:
        data_list: 要处理的数据列表
        table_key: 表格配置键，默认为"fba_feediff"
        key_fields: 用作唯一键的字段组合，默认为("店铺", "国家")
    """
    existing_map = fetch_all_records(table_key, key_fields)
    to_create, to_update = split_new_and_update(data_list, existing_map, key_fields)
    if to_create:
        batch_create_feishu_records(to_create, table_key)
    if to_update:
        batch_update_feishu_records(to_update, table_key)