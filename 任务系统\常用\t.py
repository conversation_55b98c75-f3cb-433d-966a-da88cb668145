from utils_mrc.MysqlHelper import *
import datetime


def get_batch_daily_duration(start_date, end_date):
    # 将日期转换为Unix时间戳
    start_timestamp = int(datetime.datetime.strptime(start_date, '%Y-%m-%d %H:%M:%S').timestamp())
    end_timestamp = int(datetime.datetime.strptime(end_date, '%Y-%m-%d %H:%M:%S').timestamp())

    # 查询指定时间段内每个批次的任务数量、总耗时和平均耗时
    query = """
    SELECT 
        DATE(FROM_UNIXTIME(create_time)) AS batch_date,
        create_time AS batch_id,
        COUNT(*) AS task_count,
        SUM(done_time - execute_time) AS total_duration,
        AVG(done_time - execute_time) AS average_duration
    FROM 
        `rpa`.`task_asins_loc`
    WHERE 
        execute_time >= %s AND execute_time <= %s
        AND done_time > 0 AND execute_time > 0
    GROUP BY 
        DATE(FROM_UNIXTIME(create_time)), create_time
    ORDER BY 
        create_time
    """

    rs = MSXS.get_dict(query, (start_timestamp, end_timestamp))

    for r in rs:
        batch_date = r['batch_date']
        batch_id = r['batch_id']
        task_count = r['task_count']
        total_duration = r['total_duration']
        average_duration = r['average_duration']

        # 将 total_duration 和 average_duration 转换为浮点数
        total_duration_float = float(total_duration)
        average_duration_float = float(average_duration)

        # 将总耗时和平均耗时转换为datetime.timedelta对象
        total_delta = datetime.timedelta(seconds=total_duration_float)
        avg_delta = datetime.timedelta(seconds=average_duration_float)

        print(f"日期: {batch_date}, 批次ID: {batch_id}, 任务数量: {task_count}, 总耗时: {total_delta}, 平均耗时: {avg_delta}")


# 示例调用
start_date = '2024-11-20 00:00:00'
end_date = '2024-12-07 23:59:59'
get_batch_daily_duration(start_date, end_date)
