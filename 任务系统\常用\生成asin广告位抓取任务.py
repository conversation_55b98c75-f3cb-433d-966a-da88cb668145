import random

from utils_mrc.MysqlHelper import *
from utils_mrc.ExcelProcessor import *

from work.自动化.卖家精灵.SellerSpriteSpider import get_key_by_value


def create_task(data):
    MS.insert_many("""
    INSERT INTO `rpa`.`task_asins_loc` (`app_id`, `platform`, `site`, `title`, `contents`, `user_id`, `username`, `page`, `start_time`, `end_time`, `datetime`, `create_time`) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, data)
    print(MS.err) if MS.err else print('抓取任务创建成功！')


def create_task_cron(data):
    MS.insert_many("""
    INSERT INTO `rpa`.`tasks_cron` (`app_id`, `platform`, `site`, `title`, `url`, `user_id`, `username`, `page`, `crawl_frequency`, `create_time`) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, data)
    print(MS.err) if MS.err else print('定时任务创建成功！')


MS = MSXS
app_id = 7  # 状态
df = pd.read_excel(r'D:\Downloads\广告关键词爬取.xlsx')
# 计算每个值的出现次数
# value_counts = df['关键词'].value_counts()
# # 筛选出重复值
# duplicates = value_counts[value_counts > 1]
# # 输出结果
# print("重复值及其出现次数:")
# print(duplicates)
# exit()
unique_data = set()
insert_data = []
insert_data_cron = []
for index, row in df.iterrows():
    site = row[0].lower()
    amazon_url_site = get_key_by_value(site)
    key = '+'.join(row[1].lower().strip().split(' '))
    title = f'每日任务：{key}'
    contents = f'https://www.amazon.{amazon_url_site}/s?k={key}'
    if contents in unique_data:
        continue
    unique_data.add(contents)
    if site and amazon_url_site and key:
        list_d = (app_id, 'amazon', site, title, contents, 60, '李莉', 3, get_today_zero_timestamp(), get_end_of_day_timestamp(), get_today_zero_timestamp(), now_int())
        list_d_c = (app_id, 'amazon', site, title, contents, 60, '李莉', 3, 'day', now_int())
        insert_data.append(list_d)
        insert_data_cron.append(list_d_c)
    else:
        print('数据不完整', row)
        break

print(f'共{len(insert_data)}个数据')
create_task(insert_data)
# create_task_cron(insert_data_cron)
