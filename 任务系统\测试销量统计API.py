# -*- coding:UTF-8 -*-
# @FileName  :测试销量统计API.py
# @Time      :2025/7/24 
# <AUTHOR>

import time
import traceback
from datetime import datetime, timedelta
from utils_mrc.pub_fun import *
from utils_mrc.MysqlHelper import MS
from work.自动化.领星抓取.LingXingSessionPage import LingXingSessionPage


def test_new_params():
    """测试新的参数设置"""
    
    session = LingXingSessionPage(user='jszg01')
    
    # 计算日期范围：从15天前到今天
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=14)).strftime('%Y-%m-%d')
    
    logging(f"测试新参数设置")
    logging(f"日期范围: {start_date} 到 {end_date}")
    
    # 使用你提供的完整参数
    test_params = {
        'asin_type': 'msku',
        'bid': '',
        'cid': '',
        'currency_code': '',
        'date_type': '1',
        'delivery_methods': [],
        'developers': [],
        'end': end_date,
        'field_key': 't-sales-stat-fix-msku',
        'gtag_ids': '',
        'length': 500,
        'mids': '',
        'offset': 0,
        'order_type': 0,
        'req_time_sequence': '/api/report/asinDailyLists$$6',
        'search_field': 'asin',
        'search_value': [],
        'seller_relation_uids': '',
        'sids': '',
        'sort_field': 'total',
        'sort_type': 'desc',
        'start': start_date,
        'turn_on_summary': '0',
        'type': 'volume'
    }
    
    try:
        # 设置请求头
        session.page.set.header("auth-token", session.get_token())
        
        url = "https://muke.lingxing.com/api/report/asinDailyLists"
        logging(f"请求URL: {url}")
        logging(f"请求参数: {test_params}")
        
        # 发送请求
        session.page.post(url, json=test_params)
        
        # 获取响应
        result = session.check_page_json()
        
        logging(f"API响应成功!")
        
        # 检查响应结构
        if isinstance(result, dict):
            logging(f"响应键: {list(result.keys())}")
            
            if 'total' in result:
                logging(f"总数据量(total): {result['total']}")
            if 'count' in result:
                logging(f"总数据量(count): {result['count']}")
            
            if 'list' in result:
                logging(f"数据列表长度: {len(result['list'])}")
                if result['list']:
                    logging(f"第一条数据的键: {list(result['list'][0].keys())}")
                    
                    # 检查日期字段
                    first_item = result['list'][0]
                    date_fields = []
                    for key in first_item.keys():
                        if isinstance(key, str) and len(key) == 10 and key.count('-') == 2:
                            try:
                                datetime.strptime(key, '%Y-%m-%d')
                                date_fields.append(key)
                            except ValueError:
                                continue
                    
                    logging(f"找到的日期字段: {date_fields}")
                    
                    if date_fields:
                        sample_date = date_fields[0]
                        logging(f"示例日期数据 {sample_date}: {first_item[sample_date]}")
            
            if 'code' in result:
                logging(f"响应代码: {result['code']}")
                
            if 'message' in result:
                logging(f"响应消息: {result['message']}")
                
        return result
        
    except Exception as e:
        logging(f"API调用异常: {e}")
        traceback.print_exc()
        return None


def test_api_method():
    """测试封装的API方法"""
    
    logging(f"\n=== 测试封装的API方法 ===")
    
    session = LingXingSessionPage(user='jszg01')
    
    try:
        # 测试获取总数
        total = session.api_sales_list(only_total=True)
        logging(f"API方法返回总数: {total}")
        
        # 测试获取数据列表
        if total > 0:
            data_list = session.api_sales_list(param_payload={'length': 20})  # 先测试少量数据
            logging(f"API方法返回数据列表长度: {len(data_list)}")
            
            if data_list:
                logging(f"第一条数据示例: {data_list[0]}")
        
    except Exception as e:
        logging(f"API方法调用异常: {e}")
        traceback.print_exc()


if __name__ == '__main__':
    logging(f'开始测试新的销量统计API参数...')
    
    # 1. 测试新参数设置
    result = test_new_params()
    
    # 2. 测试封装的API方法
    if result:
        test_api_method()
    
    logging(f'测试完成')
