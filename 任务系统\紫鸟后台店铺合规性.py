# -*- coding:UTF-8 -*-
# @FileName  :main.py
# @Time      :2024/6/18 11:27
# <AUTHOR>
from utils_mrc.pub_fun import *
from threading import Thread
from work.自动化.紫鸟.ZiNiao import *


def main(manager):
    main_tasks = MS.get_dict(
            f'SELECT id, app_id, contents, user_id, username, datetime, site, page, title  FROM rpa.`task_amazon_rule`  WHERE STATUS = 1 ORDER BY priority desc, id asc'
    )
    if not main_tasks:
        time.sleep(10)
        return
    for task in main_tasks:
        task_id = task.get('id')
        execute_time = now_int()
        # 修改所关联任务状态为执行中
        if MS.update('UPDATE rpa.`task_amazon_rule` SET status = 2,`execute_time` = %s WHERE id = %s and `status` = 1', (now_int(), task_id)) < 1:
            logging(f'当前任务已经在执行')
            return False
        app_id = task.get('app_id')
        contents = task.get('contents').strip()
        user_id = task.get('user_id')
        username = task.get('username')
        task_time = task.get('task_time')  # 关联任务的所属时间`datetime`
        site = task.get('site')
        page = task.get('page')
        title = task.get('title')
        date_time = task.get('datetime') or get_today_zero_timestamp()  # 当天0点时间戳
        task_params = {  # 更新任务表/数据表记录的字段
            'task_id': task_id,
            'app_id': app_id,
            'user_id': user_id,
            'username': username,
            'task_time': task_time,
            'date_time': date_time,
            'platform_num': 0,
            'task_num': 0,
            'site': site,
            'result': '完成',
            'status': 10,
            'finally_result': '',
            'finally_status': 10,
            'cur_page': 1,  # 当前页
            'real_page': 1,  # 已操作的页数
            'items_per_page': 0,  # 单页数量
            'total_items': 0,  # 总数量
            'total_pages': 0,  # 总页数
        }
        manager.task_params.update(task_params)

        store_list = split_get_list(contents)
        # 开始抓取任务
        print("=====获取商店列表=====")
        browser_list = manager.get_browser_list()
        if browser_list is None:
            manager.task_params['result'] = f'{manager.user_info["username"]}商店列表为空'
            manager.task_params['finally_result'] += manager.task_params['result']
            manager.task_params['finally_status'] = 20
            fsmsg.send(None, manager.task_params['result'])
        else:
            store_info_list = []
            not_found_message = []

            if contents == '全部':
                store_info_list = browser_list
                excluded_names = 'XFF、STB、SIC、STB、us、feng、封'.lower().split('、')  # 不指定抓取全部时，需要排除的店铺名称
                store_info_list = [x for x in store_info_list if not any(name.lower() in x['browserName'].lower() for name in excluded_names)]
                fetch_count = len(browser_list)
            else:
                # 预处理browser_list，将其转换为字典以提高查找效率
                browser_dict = {x['browserName'].strip(): x for x in browser_list}
                for store in store_list:
                    store_name = store.strip()
                    store_info = browser_dict.get(store_name)
                    if store_info:
                        store_info_list.append(store_info)
                    else:
                        not_found_message.append(f'|未找到店铺【{store}】')
                fetch_count = len(store_list)
            if not_found_message:
                manager.task_params['result'] = ''.join(not_found_message)
                manager.task_params['finally_result'] += manager.task_params['result']
                # manager.task_params['status'] = 20

            logging(f'当前应用ID:{app_id},任务ID:{task_id},本次待抓取店铺总数:{fetch_count}')
            """循环打开所有店铺运行脚本"""
            if store_info_list:
                manager.use_all_browser_run_task(store_info_list)

        status = max(manager.task_params['finally_status'], manager.task_params['status'])
        result = '成功' if status == 10 else manager.task_params['finally_result']
        if contents == '全部' and result != 10:
            rs = re.findall(r'【(.*?)】', result)
            if rs:
                rs = list(set(rs))
                new_content = ','.join(rs)
                MS.insert(
                        'insert into task_amazon_rule (app_id,cid,platform,title,contents,user_id,username,start_time,end_time,datetime,create_time) values (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)',
                        (9, 1, 'amazon', f'任务{task_id}-异常重抓({len(rs)})', new_content, 18, 'zc', get_today_zero_timestamp(), get_end_of_day_timestamp(),
                         get_today_zero_timestamp(),
                         now_int(),)
                )
                status = 10
        platform_num = manager.task_params['platform_num']
        task_num = manager.task_params['task_num']
        done_time = now_int()
        # 更新任务状态
        MS.update(
                f'update `task_amazon_rule` set status = %s, result = %s, platform_num = %s, task_num = %s,done_time=%s where id = %s',
                (status, result, platform_num, task_num, done_time, task_id)
        )
        logging(f'任务:{task_id} 已执行结束 平台数量：{platform_num} 抓取数量:{task_num} 耗时:{done_time - execute_time}s')


def check_cron_task():
    logging('已执行定时任务检查')

    def create_task():
        # 判断最新任务所属时间尚在一天内，查询不到则可生成
        rs = MS.get_one("""
            select 1 from task_amazon_rule
            where cid = 1 
            and NOW() < FROM_UNIXTIME(datetime) + interval 1 day
            and id = (select max(id) from task_amazon_rule where cid =1)
        """)
        if rs:
            logging('未能生成任务')
            return
        logging('可生成任务')
        MS.insert(
                'insert into task_amazon_rule (app_id,cid,platform,title,contents,user_id,username,start_time,end_time,datetime,create_time) values (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)',
                (9, 1, 'amazon', '每日抓取-all', '全部', 18, 'zc', get_today_zero_timestamp(), get_end_of_day_timestamp(), get_today_zero_timestamp(), now_int(),)
        )
        if not MS.err:
            logging('每日抓取任务生成任务成功')

    while True:
        create_task()
        time.sleep(60 * 60 * 3)


# MS.insert(
#                 'insert into task_amazon_rule (app_id,cid,platform,title,contents,user_id,username,start_time,end_time,datetime,create_time) values (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s)',
#                 (9, 1, 'amazon', '测试', 'AM_ZF(EU)', 18, 'zc', get_today_zero_timestamp(), get_end_of_day_timestamp(), get_today_zero_timestamp(), now_int(),)
#         )
# exit()
if __name__ == "__main__":
    logging(f'开始运行，正在初始化程序{get_cur_run_file()}...')
    Thread(target=check_cron_task).start()
    manager = StoreManager(max_workers=5)

    while True:
        try:
            main(manager)
        except Exception as e:
            err = traceback.format_exc()
            print(err)
            msg = f'{get_cur_run_file()}主程序异常！'
            logging(msg)
            fsmsg.send(None, msg, err)
            manager = StoreManager(max_workers=1)
            manager.reboot()
