import requests

cookies = {
    '_ga': 'GA1.1.2103117073.1753778225',
    '_gcl_au': '1.1.1270156501.1753778225',
    '691fdc40679496add736': '7119012ca542f22195167f00bfc99d0a',
    '_fp': '50fb5163d2f1b5ec08f6c2ea85e182f3',
    'MEIQIA_TRACK_ID': '30XeE1BtGvUfEoJlrbQIYnhCBlV',
    'MEIQIA_VISIT_ID': '30XeE1Pc9dCA1R4U5vmO0lXFqna',
    'JSESSIONID': 'E7F01EC738FAC732BE39B24C257D40FA',
    '_gaf_fp': '20f9030e7737d7ae5bd767bd087fb250',
    'JSESSIONID': '66F72C1F6CAED9B82CF3D930619D4BFA',
    'Sprite-X-Token': 'eyJhbGciOiJSUzI1NiIsImtpZCI6IjE2Nzk5NjI2YmZlMDQzZTBiYzI5NTEwMTE4ODA3YWExIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ClfXLG0SCqlIler3jL79wZzCW390EJ0Nv6xTKZeFqbiMxLPOvHfVNBOm2pkj3nnsRpqMw-pL6MpOUE5GxYe-zIiD_pacc_8fmRcKt8n41e8blSzYNwqiyWBq_YrzXqONKko95f8qE1HDaFECNHAwaBme_PMgN0-MANWTZrohR-f6qnDBgHDrjYXq-JYmrLUxZ1F4jmzz1SYrxQM1eflyANYdwAD068gUcjhat_gQup8RD4qzG7TmcGUJ46TF2qB9w-zPEhqhFp7j16Zna46G1yW5K-AN_n8Z8d3Q5nJTOp5Bpi2B_uosDgMNi1gR3ShTa8-IpT9VVRnryVPLuBgURw',
    'ao_lo_to_n': '4062083571A4riEFb+f8XgcCvhobBRrZ2yEcDyx0DTtmbdi1vsIn9tHkpiJmb+jWDv6MkVDyg6J7a6//q9EoQLJC5smzczUVzoQDSCfbFmga2Y6icokVQ=',
    'rank-login-user': '4062083571A4riEFb+f8XgcCvhobBRrfFTbqO3ksZMiZ6wQEZ1WCrkPNT5d2rnhGf8eKMw7/k2',
    'rank-login-user-info': 'eyJuaWNrbmFtZSI6Inl4eTE2OCIsImlzQWRtaW4iOmZhbHNlLCJhY2NvdW50IjoiMTg5KioqKjk2NzIiLCJ0b2tlbiI6IjQwNjIwODM1NzFBNHJpRUZiK2Y4WGdjQ3Zob2JCUnJmRlRicU8za3NaTWlaNndRRVoxV0Nya1BOVDVkMnJuaEdmOGVLTXc3L2syIn0=',
    'Hm_lvt_e0dfc78949a2d7c553713cb5c573a486': '**********',
    'Hm_lpvt_e0dfc78949a2d7c553713cb5c573a486': '**********',
    'HMACCOUNT': '8495F32F2DFDA3D7',
    '_ga_38NCVF2XST': 'GS2.1.s1753778225$o1$g1$t1753780846$j23$l0$h12132676',
}

headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-CN,zh;q=0.9',
    'content-type': 'application/json;charset=UTF-8',
    'origin': 'https://www.sellersprite.com',
    'priority': 'u=1, i',
    'referer': 'https://www.sellersprite.com/v3/product-research?market=DE&page=1&size=60&symbolFlag=false&monthName=bsr_sales_nearly&selectType=2&filterSub=false&weightUnit=g&order%5Bfield%5D=total_units&order%5Bdesc%5D=true&productTags=%5B%5D&nodeIdPaths=%5B%5D&sellerTypes=%5B%5D&pkgDimensionTypeList=%5B%5D&sellerNationList=%5B%22CY%22%5D&smallAndLight=N&video=',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36',
    # 'cookie': '_ga=GA1.1.2103117073.1753778225; _gcl_au=1.1.1270156501.1753778225; 691fdc40679496add736=7119012ca542f22195167f00bfc99d0a; _fp=50fb5163d2f1b5ec08f6c2ea85e182f3; MEIQIA_TRACK_ID=30XeE1BtGvUfEoJlrbQIYnhCBlV; MEIQIA_VISIT_ID=30XeE1Pc9dCA1R4U5vmO0lXFqna; JSESSIONID=E7F01EC738FAC732BE39B24C257D40FA; _gaf_fp=20f9030e7737d7ae5bd767bd087fb250; JSESSIONID=66F72C1F6CAED9B82CF3D930619D4BFA; Sprite-X-Token=eyJhbGciOiJSUzI1NiIsImtpZCI6IjE2Nzk5NjI2YmZlMDQzZTBiYzI5NTEwMTE4ODA3YWExIn0.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ClfXLG0SCqlIler3jL79wZzCW390EJ0Nv6xTKZeFqbiMxLPOvHfVNBOm2pkj3nnsRpqMw-pL6MpOUE5GxYe-zIiD_pacc_8fmRcKt8n41e8blSzYNwqiyWBq_YrzXqONKko95f8qE1HDaFECNHAwaBme_PMgN0-MANWTZrohR-f6qnDBgHDrjYXq-JYmrLUxZ1F4jmzz1SYrxQM1eflyANYdwAD068gUcjhat_gQup8RD4qzG7TmcGUJ46TF2qB9w-zPEhqhFp7j16Zna46G1yW5K-AN_n8Z8d3Q5nJTOp5Bpi2B_uosDgMNi1gR3ShTa8-IpT9VVRnryVPLuBgURw; ao_lo_to_n=4062083571A4riEFb+f8XgcCvhobBRrZ2yEcDyx0DTtmbdi1vsIn9tHkpiJmb+jWDv6MkVDyg6J7a6//q9EoQLJC5smzczUVzoQDSCfbFmga2Y6icokVQ=; rank-login-user=4062083571A4riEFb+f8XgcCvhobBRrfFTbqO3ksZMiZ6wQEZ1WCrkPNT5d2rnhGf8eKMw7/k2; rank-login-user-info=eyJuaWNrbmFtZSI6Inl4eTE2OCIsImlzQWRtaW4iOmZhbHNlLCJhY2NvdW50IjoiMTg5KioqKjk2NzIiLCJ0b2tlbiI6IjQwNjIwODM1NzFBNHJpRUZiK2Y4WGdjQ3Zob2JCUnJmRlRicU8za3NaTWlaNndRRVoxV0Nya1BOVDVkMnJuaEdmOGVLTXc3L2syIn0=; Hm_lvt_e0dfc78949a2d7c553713cb5c573a486=**********; Hm_lpvt_e0dfc78949a2d7c553713cb5c573a486=**********; HMACCOUNT=8495F32F2DFDA3D7; _ga_38NCVF2XST=GS2.1.s1753778225$o1$g1$t1753780846$j23$l0$h12132676',
}

json_data = {
    'market': 'DE',
    'page': 1,
    'size': 60,
    'symbolFlag': True,
    'monthName': 'bsr_sales_nearly',
    'selectType': '2',
    'filterSub': False,
    'weightUnit': 'g',
    'order': {
        'field': 'total_units',
        'desc': True,
    },
    'productTags': [],
    'nodeIdPaths': [],
    'sellerTypes': [],
    'pkgDimensionTypeList': [],
    'sellerNationList': [
        'CY',
    ],
    'smallAndLight': 'N',
}

response = requests.post('https://www.sellersprite.com/v3/api/product-research', cookies=cookies, headers=headers, json=json_data)

# Note: json_data will not be serialized by requests
# exactly as it was in the original request.
#data = '{"market":"DE","page":1,"size":60,"symbolFlag":true,"monthName":"bsr_sales_nearly","selectType":"2","filterSub":false,"weightUnit":"g","order":{"field":"total_units","desc":true},"productTags":[],"nodeIdPaths":[],"sellerTypes":[],"pkgDimensionTypeList":[],"sellerNationList":["CY"],"smallAndLight":"N"}'
#response = requests.post('https://www.sellersprite.com/v3/api/product-research', cookies=cookies, headers=headers, data=data)