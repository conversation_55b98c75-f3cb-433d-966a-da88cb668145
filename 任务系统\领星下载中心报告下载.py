# -*- coding:UTF-8 -*-
# @FileName  :领星下载中心报告下载.py
# @Time      :2024/9/24 16:42
# <AUTHOR>
from utils_mrc.pub_fun import *
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.executors.pool import ThreadPoolExecutor
from work.自动化.领星抓取.LingXing import *


def scheduled_task():
    print("任务执行时间：", time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    main_download_report()


def main():
    main_download_report()
    executors = {
        'default': ThreadPoolExecutor(max_workers=20)
    }

    job_defaults = {
        'coalesce': False,
        'max_instances': 3
    }

    scheduler = BackgroundScheduler(executors=executors, job_defaults=job_defaults)
    hour = 6
    minute = 0
    second = 0
    # second = random.randint(1, 5)
    scheduler.add_job(scheduled_task, 'cron', hour=hour, minute=minute, second=second)
    logging(f'已添加广告报告下载任务，将在{hour}时{minute}分{second}秒执行！')

    scheduler.start()
    try:
        while True:
            time.sleep(5)
    except (KeyboardInterrupt, SystemExit):
        scheduler.shutdown()  # 关闭调度器


if __name__ == '__main__':
    main()
