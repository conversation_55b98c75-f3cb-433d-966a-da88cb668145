# -*- coding:UTF-8 -*-
# @FileName  :广告抓取.py
# @Time      :2024/8/5 16:42
# <AUTHOR>
from utils_mrc.pub_fun import *
from work.自动化.领星抓取.LingXing import main_fetch_ad
import random
import sys
import time
import threading
from apscheduler.schedulers.background import BackgroundScheduler
from utils_mrc.MysqlHelper import logging
from apscheduler.executors.pool import ThreadPoolExecutor


def scheduled_task():
    time.sleep(random.uniform(1, 10))
    print("任务执行时间：", time.strftime("%Y-%m-%d %H:%M:%S", time.localtime()))
    main_fetch_ad()


def main():
    scheduled_task()  # 手动执行一次

    executors = {
        'default': ThreadPoolExecutor(max_workers=20)
    }

    job_defaults = {
        'coalesce': False,
        'max_instances': 3
    }

    scheduler = BackgroundScheduler(executors=executors, job_defaults=job_defaults)
    second = random.randint(1, 5)
    scheduler.add_job(scheduled_task, 'cron', hour=7, minute=0, second=second)
    logging(f'已添加广告抓取任务，将在早晨7时{second}秒执行！')

    scheduler.start()
    try:
        while True:
            time.sleep(5)
    except (KeyboardInterrupt, SystemExit):
        scheduler.shutdown()  # 关闭调度器


if __name__ == '__main__':
    main()
