# -*- coding:UTF-8 -*-
# @FileName  :领星广告指标采集.py
# @Time      :2024/10/11 15:17
# <AUTHOR>
from utils_mrc.pub_fun import *
import time
from threading import Thread
from work.自动化.领星抓取.LingXing import *


def main(wait_hour=2):
    while True:
        # current_hour = datetime.now().hour
        # if not (0 <= current_hour < 7):
        #     create_task()
        # else:
        #     logging('当前时间在凌晨0-6点之间，跳过任务执行')
        main_fetch_ad_metrics()
        time.sleep(60 * 60 * wait_hour)  # 每两小时检查一次


# MS = MSXS
# work.自动化.领星抓取.LingXing.MS = MS

if __name__ == '__main__':
    logging(f'开始运行，正在初始化程序{get_cur_run_file()}...')
    while True:
        try:
            main(wait_hour=2)
        except Exception as e:
            err = traceback.format_exc()
            print(err)
            msg = '主程序抓取异常！'
            logging(msg)
            fsmsg.send(None, msg, err)
