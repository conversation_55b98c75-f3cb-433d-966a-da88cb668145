# -*- coding:UTF-8 -*-
from utils_mrc.pub_fun import *
from work.自动化.任务系统.领星报表生成应用 import *
import schedule
import time
import traceback


def run():
    try:
        for user in users:
            main_run_task_report_generate(user)
    except Exception as e:
        err = traceback.format_exc()
        print(err)
        fsmsg.send('订单/库存报表生成主程序抓取异常！', '订单/库存报表生成主程序抓取异常！', err)


def main():
    while True:
        try:
            # 检查是否有定时任务需要执行
            schedule.run_pending()

        except Exception as e:
            err = traceback.format_exc()
            print(err)
            fsmsg.send('订单/库存报表生成主程序抓取异常！', '订单/库存报表生成主程序抓取异常！', err)

        # 等待x秒，避免CPU占用过高
        time.sleep(10)


if __name__ == '__main__':
    logging(f'开始运行，正在初始化程序{get_cur_run_file()}...')
    # 设置xx执行一次 run 函数
    # schedule.every().hour.do(run)
    schedule.every().day.at('08:55').do(run)
    schedule.every().day.at('13:30').do(run)
    schedule.every().day.at('18:00').do(run)
    schedule.every().day.at('19:45').do(run)
    schedule.every().day.at('21:00').do(run)
    main()
