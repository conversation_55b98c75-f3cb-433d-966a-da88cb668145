import os
import sys
import time
import pandas as pd
import schedule
import traceback
from typing import List, Dict
from datetime import datetime

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from utils_mrc.pub_fun import logging
from utils_mrc.MysqlHelper import MS
from work.自动化.领星抓取.DataFetcher import FetchConfig, DataFetcher
from work.自动化.领星抓取.ShipmentTrackingProvider import LingXingShipmentTrackingProvider
from work.自动化.任务系统.常用.feishu_bitable import upsert_data_batch_to_feishu_bitable


def now_int():
    return int(time.time())

def get_today_zero_timestamp():
    from datetime import datetime, time as dtime
    return int(datetime.combine(datetime.today(), dtime.min).timestamp())


def save_shipment_tracking_data(data: List[Dict], params: Dict):
    """保存货件签收差异数据到MySQL和飞书"""

    if not data:
        return

    df = pd.DataFrame(data)

    # 添加缺失字段
    df['create_time'] = datetime.now()
    df['update_time'] = datetime.now()

    # 删除 id 字段（如果存在）
    if 'id' in df.columns:
        df.drop(columns=['id'], inplace=True)

    # 保证字段顺序
    expected_columns = [
        'msku', 'fnsku', 'asin', 'shipment_id', 'shipment_name', 'tracking_number',
        'store', 'country', 'declared_quantity', 'shipped_quantity', 'received_quantity',
        'pending_diagnosis_quantity', 'pending_diagnosis_amount', 'remaining_diagnosis_days',
        'status', 'remark', 'create_time', 'update_time'
    ]
    df = df[[col for col in expected_columns if col in df.columns]]

    try:
        # 构造 SQL - 使用 shipment_id + msku + store 作为唯一键
        table = 'rpa.data_lingxing_shipment_tracking'
        columns = ', '.join([f"`{col}`" for col in df.columns])
        placeholders = ', '.join(['%s'] * len(df.columns))
        # 构造 ON DUPLICATE KEY UPDATE 部分（排除主键id和唯一索引字段）
        update_columns = [col for col in df.columns if col not in ('id', 'shipment_id', 'msku', 'store')]
        update_clause = ', '.join([f"`{col}`=VALUES(`{col}`)" for col in update_columns])
        sql = f"INSERT INTO {table} ({columns}) VALUES ({placeholders}) ON DUPLICATE KEY UPDATE {update_clause}"

        # 转为 tuple 列表
        args = [tuple(row) for row in df.values]

        logging(f"准备插入表 {table}，共 {len(args)} 条（支持shipment_id+msku+store唯一自动更新）")
        MS.insert_many(sql, args)

        # ========== 推送到飞书多维表格 ===========
        feishu_field_map = {
            "msku": "MSKU",
            "fnsku": "FNSKU", 
            "asin": "ASIN",
            "shipment_id": "货件编号",
            "shipment_name": "货件名称",
            "tracking_number": "追踪编号",
            "store": "店铺",
            "country": "国家",
            "declared_quantity": "申报量",
            "shipped_quantity": "已发货量",
            "received_quantity": "签收量",
            "pending_diagnosis_quantity": "预计可诊断数量",
            "pending_diagnosis_amount": "预计可诊断金额",
            "remaining_diagnosis_days": "剩余可诊断天数",
            "status": "状态",
            "remark": "备注"
        }
        
        # 需要为数字的字段
        feishu_number_fields = {
            "申报量", "已发货量", "签收量", "预计可诊断数量", 
            "预计可诊断金额", "剩余可诊断天数"
        }
        
        feishu_data_list = []
        for row in df.to_dict(orient='records'):
            feishu_data = {}
            for db_field, feishu_field in feishu_field_map.items():
                value = row.get(db_field, "")
                if feishu_field in feishu_number_fields:
                    try:
                        value = float(value) if value not in (None, "") else 0
                    except Exception:
                        value = 0
                feishu_data[feishu_field] = value
            
            # 时间字段格式化
            if row.get("create_time"):
                if isinstance(row["create_time"], datetime):
                    dt_str = row["create_time"].strftime("%Y-%m-%d %H:%M:%S")
                else:
                    dt_str = str(row["create_time"])
            else:
                dt_str = ""
            feishu_data["创建时间"] = dt_str
            feishu_data["更新时间"] = dt_str
            feishu_data_list.append(feishu_data)
        
        if feishu_data_list:
            # 使用货件签收差异表格，使用货件编号+MSKU作为唯一键
            upsert_data_batch_to_feishu_bitable(
                feishu_data_list, 
                table_key="shipment_tracking",
                key_fields=("货件编号", "MSKU")
            )
        # ========== END ===========
        
    except Exception as e:
        logging(f"插入货件签收差异数据失败: {e}")


def print_results(results: Dict):
    """打印抓取结果"""
    print("\n抓取结果统计:")
    logging(f"总数据量: {len(results.get('data', []))}")
    print("\n账号统计:")
    for user, stats in results.get('user_stats', {}).items():
        logging(f"{user}: 成功 {stats['success']} 页, 失败 {stats['failed']} 页")
    if results.get('failed_pages'):
        print("\n失败页面:")
        for page, retries in results.get('failed_pages', {}).items():
            logging(f"页码 {page}: 重试 {retries} 次")


def run_shipment_tracking_fetch(users: list):
    """
    抓取货件签收差异数据
    """
    fetch_params = {
        'length': 200,
        'offset': 0
    }

    config = FetchConfig(
        data_provider=LingXingShipmentTrackingProvider(),
        users=users,
        task_params={
            'task_id': 200,
            'app_id': 2,
            'datetime': get_today_zero_timestamp(),
            'task_time': now_int()
        },
        fetch_params=fetch_params,
        save_data_func=save_shipment_tracking_data  # 👈 自定义保存
    )

    logging(f"\n开始抓取货件签收差异数据")

    fetcher = DataFetcher(config)
    results = fetcher.fetch_data()
    print_results(results)


def scheduled_shipment_tracking_task():
    """
    定时执行的货件签收差异抓取任务
    """
    try:
        logging("开始执行定时货件签收差异抓取任务...")
        run_shipment_tracking_fetch(users=['jszg01', 'yxyJS2'])
        logging("定时货件签收差异抓取任务执行完成")
    except Exception as e:
        err = traceback.format_exc()
        logging(f"定时货件签收差异抓取任务执行异常: {err}")


def main_scheduler():
    """
    主调度器，设置定时任务并运行
    """
    logging("启动领星货件签收差异定时抓取任务调度器")
    
    # 启动时立即执行一次
    logging("启动时立即执行一次货件签收差异抓取...")
    scheduled_shipment_tracking_task()
    
    # 设置定时任务：每天8点和晚上9点执行
    schedule.every().day.at("08:00").do(scheduled_shipment_tracking_task)
    schedule.every().day.at("21:00").do(scheduled_shipment_tracking_task)
    
    logging("定时任务已设置：每天 08:00 和 21:00 执行货件签收差异抓取")
    
    while True:
        try:
            # 检查是否有定时任务需要执行
            schedule.run_pending()
        except Exception as e:
            err = traceback.format_exc()
            logging(f"调度器运行异常: {err}")
        
        # 等待10秒，避免CPU占用过高
        time.sleep(10)


if __name__ == '__main__':
    import sys
    
    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] == '--scheduler':
        # 运行定时任务模式
        main_scheduler()
    else:
        # 运行单次执行模式
        logging("启动：领星货件签收差异抓取任务（单次执行模式）")
        run_shipment_tracking_fetch(users=['jszg01', 'yxyJS2'])


# python 领星货件签收差异.py --scheduler
