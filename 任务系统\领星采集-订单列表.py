# -*- coding:UTF-8 -*-
# @FileName  :领星采集-订单列表.py
# @Time      :2025/2/5 19:36
# <AUTHOR>

from utils_mrc.pub_fun import *
from work.自动化.领星抓取.DataFetcher import *
from TimePinner import Pinner


def main():
    users = ['jszg01', 'yxyJS2']
    params = {
        'task_id': 1,
        'app_id': 1,
        'datetime': get_today_zero_timestamp(),
        'task_time': now_int()
    }
    
    # 测试订单抓取
    order_provider = LingXingOrderProvider()
    order_config = FetchConfig(
            data_provider=order_provider,
            users=users,
            task_params=params,
    )

    logging("=== 测试订单数据抓取 ===")
    fetcher = DataFetcher(order_config)
    results = fetcher.fetch_data()
    print_results(results)



if __name__ == '__main__':
    main()
