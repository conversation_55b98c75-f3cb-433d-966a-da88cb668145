# 假设C_.version()是一个协程函数
# class C_:
#     @staticmethod
#     def version():
#         return {'version': '4.5.0'}

# 定义bl类，用于处理加密令牌的生成
class bl:
    # Google TKK 默认值
    GOOGLE_TKK_DEFAULT = "446379.1364508470"

    # 初始化类，设置扩展版本号
    @classmethod
    def init(cls):
        # version_info = C_.version()
        # cls.EXT_VERSION = re.sub(r'\.', '00', version_info["version"].replace('.', '0')) + ".1364508470"
        cls.EXT_VERSION = '400500.1364508470'
        # print(cls.EXT_VERSION)

    # 生成Google令牌
    @classmethod
    def google_token(cls, e, t):
        try:
            tkk = cls.update_token(e)
            return cls._cal(t, tkk) if tkk and tkk != "" else cls._cal(t, cls.GOOGLE_TKK_DEFAULT)
        except Exception as ex:
            raise ex

    # 生成Sellersprite令牌
    @classmethod
    def sellersprite_token(cls, e, t, r, n):
        s = []
        a = [e, t, r, n]

        for item in a:
            if item and item is not None and len(str(item)) > 0:
                temp = len(s)
                if isinstance(item, list):
                    for i in range(len(item)):
                        s.append(item[i])
                if temp == len(s):
                    s.append(str(item))

        return "" if len(s) < 1 else cls._cal("".join(map(str, s)), cls.EXT_VERSION)

    # 更新令牌TKK，目前返回默认值
    @classmethod
    def update_token(cls, e):
        return cls.GOOGLE_TKK_DEFAULT

    # 计算令牌的私有方法
    @classmethod
    def _cal(cls, e, t):
        def r(e, t):
            for i in range(0, len(t) - 2, 3):
                n = t[i + 2]
                n = ord(n) - 87 if n >= "a" else int(n)
                n = e >> n if t[i + 1] == "+" else e << n
                e = (e + n) & 4294967295 if t[i] == "+" else e ^ n
            return e

        n = t.split(".")
        t = int(n[0]) or 0
        s = []
        a = 0
        for i in range(len(e)):
            o = ord(e[i])
            if o < 128:
                s.append(o)
            else:
                if o < 2048:
                    s.append((o >> 6) | 192)
                elif 55296 == (64512 & o) and i + 1 < len(e) and 56320 == (64512 & ord(e[i + 1])):
                    o = 65536 + ((1023 & o) << 10) + (1023 & ord(e[i + 1]))
                    s.append((o >> 18) | 240)
                    s.append((o >> 12) & 63 | 128)
                else:
                    s.append((o >> 12) | 224)
                    s.append((o >> 6) & 63 | 128)
                s.append(63 & o | 128)

        e = t
        for i in range(len(s)):
            e = r(e + s[i], "+-a^+6")

        e = r(e, "+-3^+b+-f")
        e ^= int(n[1]) or 0
        if e < 0:
            e = ********** + (********** & e)

        result = e % 1000000
        return f"{result}.{result ^ t}"


# 生成Sellersprite令牌的任务函数
def task(asin_str):
    bl.init()  # 初始化类

    tk = bl.sellersprite_token(None, asin_str, None, None)
    print(f"生成tk=>{tk}")
    return tk


# 登录函数，生成登录所需的令牌
def login(account, passwd):
    bl.init()  # 初始化类

    tk = bl.sellersprite_token(account, passwd, None, None)
    print(f"生成tk=>{tk}")
    return tk
