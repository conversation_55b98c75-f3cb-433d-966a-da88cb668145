"""
Author: Yang
Date: 2024.9.28
Description: 卖家精灵插件端-逆向
Version: 1.0.0

Usage:
    1.用令牌去鉴权，cookie有没有都行，cookie都是来自网页端的，不影响业务使用;
    2.传入站点代码和asin列表，返回接口数据

Dependencies:
    export_tk,requests,urllib,json

Example:
    None

License:
    None

Contact:
    [你的联系信息，例如电子邮件地址]

"""
import requests
from urllib.parse import quote
import export_tk


class GetDataJson:
    def __init__(self, token):
        # 基本都是固定值，插件更新会有变动，这里是4.5.0的插件固定值
        # 插件id
        self.extension = "libkfdihmladjiijocodhhkkkbjiadpd"
        # 当前语言
        self.language = "zh_CN"
        # 插件版本号
        self.version = "4.5.0"
        # 基础列表页一级路由
        self.host = "https://www.sellersprite.com/v2/extension/competitor-lookup/"
        # 全局登录令牌
        self.token = token

    # 只写了列表页的接口案例，其他的大差不差
    def getList(self, country, asin_list):
        asin_str = ""
        if len(asin_list) > 1:
            asin_str = ",".join(asin_list)
        else:
            asin_str = asin_list[0]
        req_str = quote(asin_str, encoding='utf-8')
        print(asin_str)
        # 随入参变化而变化
        tk = export_tk.task(asin_str=asin_str)
        url = f"{self.host}quick-view/{country}?asins={req_str}&source=offline&miniMode=false&withRelation=true&withSaleTrend=false&tk={tk}&version={self.version}&language={self.language}&extension={self.extension}"

        headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9,en-GB;q=0.8,en;q=0.7,en-US;q=0.6',
            'auth-token': self.token,
            'priority': 'u=1, i',
            'random-token': '4cca5efa-ebd8-446e-a905-4b27b6492f4a',  # 这里不鉴权，随意
            'sec-ch-ua': '"Not)A;Brand";v="99", "Microsoft Edge";v="127", "Chromium";v="127"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'none',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'content-type': 'application/json',
            'Host': 'www.sellersprite.com',
            'Connection': 'keep-alive'
        }
        print(url, headers)
        response = requests.request("GET", url, headers=headers)
        # print(response.text)
        if response.status_code == 200:
            return response.json()
        else:
            return None


# 示例代码
if __name__ == '__main__':
    get_api_data = GetDataJson(token="9631984271D+trYWXUMAS4FCBcRnKR+bvcvx0N6x9ZP0dKZcCpag1kAd2q61Z72NqCJ55tBdkX")
    get_asin_data = get_api_data.getList(
            country="FR",
            asin_list=[
                'B0BZJ9W4LW', 'B084G5XC29', 'B07CX777M6', 'B09JS56L1T', 'B0CVXRFT15', 'B0CGZL2GS8', 'B06XZ9K244', 'B01N6M0I9M', 'B08GFQF358',
                'B00D3HZKWA', 'B0D2DJWH24', 'B0070X405Q', 'B095CLQ1PT', 'B07VBG2XKK', 'B07VJT6R29', 'B08X6DCJT2', 'B07MCVYMNN', 'B0BNPRS182',
                'B000CRBEJ2', 'B0D9MB19C1', 'B00O1XHJYW', 'B0DD75R7CB', 'B07PYSXWR4', 'B0B5GQZ2RP', 'B00AI7COA4', 'B0B7RQ46LD', 'B08QFK83F3',
                'B00M9B66BU', 'B0C4HB8FR8', 'B07DLX83JM', 'B0CQT8K14X', 'B0D6724RJM', 'B0C3BC4QG2', 'B0CPM8C3X9', 'B0CW55SJRM', 'B09CGGDXT2'
            ])

    print(get_asin_data)
