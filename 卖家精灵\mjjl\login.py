import asyncio
import os
import random
import re
import requests
import json
import hashlib
import socket


# Assume C_.version() is a coroutine function
# class C_:
#     @staticmethod
#     async def version():
#         return {'version': '4.4.0'}

# Your TokenGenerator class
class bl:
    GOOGLE_TKK_DEFAULT = "446379.1364508470"

    @classmethod
    async def init(cls):
        # version_info = await C_.version()
        # cls.EXT_VERSION = re.sub(r'\.', '00', version_info["version"].replace('.', '0')) + ".1364508470"
        cls.EXT_VERSION = '400400.1364508470'
        # print(cls.EXT_VERSION)

    @classmethod
    async def google_token(cls, e, t):
        try:
            tkk = await cls.update_token(e)
            return cls._cal(t, tkk) if tkk and tkk != "" else cls._cal(t, cls.GOOGLE_TKK_DEFAULT)
        except Exception as ex:
            raise ex

    @classmethod
    def sellersprite_token(cls, e, t, r, n):
        s = []
        a = [e, t, r, n]

        for item in a:
            if item and item is not None and len(str(item)) > 0:
                temp = len(s)
                if isinstance(item, list):
                    for i in range(len(item)):
                        s.append(item[i])
                if temp == len(s):
                    s.append(str(item))

        return "" if len(s) < 1 else cls._cal("".join(map(str, s)), cls.EXT_VERSION)

    @classmethod
    async def update_token(cls, e):
        return cls.GOOGLE_TKK_DEFAULT

    @classmethod
    def _cal(cls, e, t):
        def r(e, t):
            for i in range(0, len(t) - 2, 3):
                n = t[i + 2]
                n = ord(n) - 87 if n >= "a" else int(n)
                n = e >> n if t[i + 1] == "+" else e << n
                e = (e + n) & 4294967295 if t[i] == "+" else e ^ n
            return e

        n = t.split(".")
        t = int(n[0]) or 0
        s = []
        a = 0
        for i in range(len(e)):
            o = ord(e[i])
            if o < 128:
                s.append(o)
            else:
                if o < 2048:
                    s.append((o >> 6) | 192)
                elif 55296 == (64512 & o) and i + 1 < len(e) and 56320 == (64512 & ord(e[i + 1])):
                    o = 65536 + ((1023 & o) << 10) + (1023 & ord(e[i + 1]))
                    s.append((o >> 18) | 240)
                    s.append((o >> 12) & 63 | 128)
                else:
                    s.append((o >> 12) | 224)
                    s.append((o >> 6) & 63 | 128)
                s.append(63 & o | 128)

        e = t
        for i in range(len(s)):
            e = r(e + s[i], "+-a^+6")

        e = r(e, "+-3^+b+-f")
        e ^= int(n[1]) or 0
        if e < 0:
            e = 2147483648 + (2147483647 & e)

        result = e % 1000000
        return f"{result}.{result ^ t}"


class Login:
    @staticmethod
    async def login_email(email, password, tk, version, language, extension, source):
        url = f"https://www.sellersprite.com/v2/extension/signin?email={email}&password={password}&tk={tk}&version={version}&language={language}&extension={extension}&source={source}"

        headers = {
            'Host': 'www.sellersprite.com',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Microsoft Edge";v="120"',
            'accept': 'application/json',
            'sec-ch-ua-mobile': '?0',
            'random-token': "38214734-3008-4096-a5c4-4da1cbc5948e",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-site': 'none',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'accept-language': 'zh-CN,zh;q=0.9,en-GB;q=0.8,en;q=0.7,en-US;q=0.6',
            'content-type': 'application/json',
            'Connection': 'keep-alive'
        }

        payload = ""  # You can add a payload if needed

        response = requests.request(method='GET', url=url, headers=headers, data=payload)

        return response.json()

    @staticmethod
    def generate_uuid():
        return "{:08x}-{:04x}-4{:03x}-{:03x}-{:012x}".format(
                random.getrandbits(32),
                random.getrandbits(16),
                random.getrandbits(12) | 0x4000,
                random.getrandbits(12) | 0x8000,
                random.getrandbits(48)
        )


def md5_lower(data):
    md5_hash = hashlib.md5()
    md5_hash.update(data.encode('utf-8'))
    md5_result = md5_hash.hexdigest()
    return md5_result


account = "rpa168"
password = "rpa16888"


async def main():
    await bl.init()  # Initialize the class
    # print(md5_lower("rpa168"))

    tk = bl.sellersprite_token(account, md5_lower(password), None, None)
    # print(tk)

    # Use tk in login_email
    result = await Login.login_email(account, md5_lower(password), tk, "4.4.0", "zh_CN",
                                     "libkfdihmladjiijocodhhkkkbjiadpd", "offline")
    # print(result)

    # 获取当前模块的绝对路径
    module_dir = os.path.dirname(os.path.abspath(__file__))
    # 构建目标文件的绝对路径
    file_path = os.path.join(module_dir, 'account_data', f'{account}.json')
    if result['code'] == "OK":
        # 将登录结果保存到本地 JSON 文件
        with open(file_path, "w") as json_file:
            json.dump(result, json_file)
        print('插件登录成功！')


def get_token():
    # Run the event loop
    hostname = socket.gethostname()
    # print(f"主机名称: {hostname}")
    if hostname.lower() == 'fetch01':
        asyncio.run(main())


if __name__ == "__main__":
    get_token()
