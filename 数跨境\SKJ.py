# -*- coding:UTF-8 -*-
# @FileName  :SJH.py
# @Time      :2024/8/13 14:02
# <AUTHOR>
import json
import re
import sys
import time
import traceback
from tqdm import tqdm
from DrissionPage import SessionPage
from utils_mrc.FeiShuAPI import *
import secrets


class SKJ:
    def __init__(self, json_data):
        self.cookies = json_data.get('cookies')
        self.dashboardId = json_data.get('dashboardId')
        self.page = SessionPage()
        self.page.set.cookies(self.cookies)
        fine_auth_token = self.page.cookies().as_dict().get('fine_auth_token')
        authorization = f'Bearer {fine_auth_token}'
        self.bi_version = json_data.get('jsy-server-version') or ''
        self.username = json_data.get('username') or ''
        self.sessionid = uuid()
        self.page.set.headers({
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh-<PERSON><PERSON>,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "content-type": "application/json;charset=UTF-8",
            "executeid": uuid(),
            "authorization": authorization,
            "jsy-server-version": self.bi_version,
            "origin": "https://work.shukuajing.com",
            "sessionid": self.sessionid,
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.0.0 Safari/537.36 Edg/127.0.0.0",
            "x-requested-with": "XMLHttpRequest"
        })
        fsmsg.app_table_token = self.app_table_token = 'EKR7bRB24aZIKFszqO5cn3mCn9d'
        self.table_max_count = 5000

    def main(self):
        result_data = {
            'result': '任务执行完毕，任务执行异常，请联系管理员。',
            'status': 20,
            'page_count': 0,
            'total': 0,
        }
        try:
            all_total_pages = 0  # 总页数
            all_total_count = 0  # 总记录数
            success_count = 0  # 成功录入页数
            fail_count = 0  # 异常页数
            url = f'https://work.shukuajing.com/decision/v1/group/switch/shorturl/{self.dashboardId}?type=1&entryType=1'
            self.page.get(url)
            result = self.page.json
            rs_successful = result.get('success', False)
            rs_code = result.get('code', 0)
            if not rs_successful or not rs_code or not result.get('data', {}).get('groupId'):
                result_data['result'] += f'获取分组ID失败{result}'
                result_data['status'] = 20
                tqdm.write(result_data['result'])
                return result_data
            group_data = result['data']
            group_id = group_data['groupId']
            group_name = group_data['groupName']

            url = f'https://work.shukuajing.com/decision/v1/design/report/{self.dashboardId}/config?groupId={group_id}&ignoreVisit=false&resourceSpace=0'
            self.page.get(url)
            result = self.page.json
            rs_successful = result.get('success', False)
            if not rs_successful:
                result_data['result'] += f'获取配置失败{result}'
                result_data['status'] = 20
                tqdm.write(result_data['result'])
                return result_data
            widgets = result['data'].get('designConfigure', {}).get('widgets', {})
            extends_prop = result['data'].get('designConfigure', {}).get('extends', {})

            # 新建一个数组用于存储结果
            result_tabs = {}
            # 遍历extendsProp对象，找到所有的tabs
            for key in extends_prop:
                tab_data = extends_prop[key].get('tabs', [])
                for tab in tab_data:
                    for w_id in tab['widgets']:
                        result_tabs[w_id] = {
                            'id': w_id,
                            'name': widgets[w_id]['widget'].get('name', {}).get('childNodes', [{}])[0].get('nodeValue', '未知名称'),
                            'tab_id': tab['id'],
                            'tab_name': tab['name'],
                        }

            # 初始化整体进度条
            with tqdm(total=len(widgets), desc='表格进度', unit='表', file=sys.stdout) as tb_progress:
                tqdm.write(f'获取配置成功，共{len(widgets)}个表格')

                res = fsmsg.drop_all_table()  # 清空除默认外的所有表格
                if res.get('code', 0) != 0 or not res:
                    result_data['result'] += f"数据表格初始化失败：{res.get('msg', '')}"
                    result_data['status'] = 20
                    tqdm.write(result_data['result'])
                    return result_data
                for widget_index, key in enumerate(widgets):
                    # tqdm.write('- ' * 30)
                    widget = widgets[key]['widget']
                    widget['visible'] = True
                    widget['filterValue'] = {
                        "filterType": 34,
                        "filterValue": []
                    }
                    widget['widgetIdValueMap'] = {}
                    widget['dimensionAuthorities'] = []
                    widget['page'] = 1
                    operators = widgets[key].get('operators')
                    tb_name = widget['name']['childNodes'][0].get('nodeValue') if widget.get('name') else '未知名称'
                    w_id = widget['wId']
                    skj_tb_url = f'https://work.shukuajing.com/decision/v1/design/widget/{self.dashboardId}/data'
                    table_id = ''
                    tb_progress.set_description_str(f'【{tb_name}】')
                    tb_success_count = 0
                    total_rows = 0
                    totalPages = 0

                    while True:
                        # 构建请求体
                        json_data = {
                            'sessionId': self.sessionid,
                            'widget': widget,
                            'operators': operators,
                        }
                        self.page.post(skj_tb_url, json=json_data)
                        tb_rs = self.page.json
                        if not tb_rs.get('success'):
                            value = result_tabs.get(w_id).get('name')
                            tab_name = result_tabs.get(w_id).get('tab_name')
                            tqdm.write(f"数跨境获取表格数据失败：【{tab_name}】;{w_id}:{value};errorCode:{tb_rs.get('errorCode')};errorMsg:{tb_rs.get('errorMsg')} ")
                            break
                        time.sleep(1)

                        total_rows = tb_rs['data'].get('row', 0)  # 总记录数
                        if total_rows > 0:
                            # req_limit_rows = total_rows if total_rows <= self.table_max_count else self.table_max_count # 限制条数
                            req_limit_rows = total_rows  # 全部
                            totalPages = req_limit_rows // 100 + (1 if req_limit_rows % 100 else 0)  # 计算总页数
                            tb_data = extract_table_data(tb_rs['data'])

                            if widget['page'] == 1:
                                all_total_pages += totalPages
                                all_total_count += total_rows
                                fs_add_tb_url = f'https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app_table_token}/tables'
                                req_json_data = create_table_request(tb_data, tb_name)
                                # result_tb = fsmsg.sess_ad.post(fs_add_tb_url, json=req_json_data).json()
                                result_tb = fsmsg.req_api(url=fs_add_tb_url, json=req_json_data)
                                # tqdm.write('表创建请求体：', req_json_data)
                                # tqdm.write('表创建结果：', result_tb)
                                if result_tb.get('code') == 0:
                                    # tqdm.write(f'数据表 创建成功')
                                    table_id = result_tb.get('data').get('table_id')
                                    result_tabs[w_id]['fs_table_id'] = table_id
                                elif result_tb.get('msg') == 'TableNameDuplicated':
                                    tqdm.write(f'【{tb_name}】 已存在')
                                    table_id = result_tabs[w_id].get('fs_table_id')
                                    if not table_id:
                                        result_data['status'] = 20
                                        result_data['result'] += f'获取【{tb_name}】 飞书表格ID失败，中止该表抓取，请先联系管理员！'
                                        tqdm.write(result_data['result'])
                                        return result_data
                                else:
                                    result_data['result'] += f'【{tb_name}】 创建失败{result_tb}'
                                    tqdm.write(result_data['result'])
                                    return result_data
                            if table_id:
                                # 转换数据
                                req_json_data_record = convert_to_records(tb_data)
                                fs_add_rd_url = f'https://open.feishu.cn/open-apis/bitable/v1/apps/{self.app_table_token}/tables/{table_id}/records/batch_create'
                                # result_rd = fsmsg.sess_ad.post(fs_add_rd_url, json=req_json_data_record).json()
                                result_rd = fsmsg.req_api(url=fs_add_rd_url, json=req_json_data_record)
                                # tqdm.write('记录创建请求体：', req_json_data_record)
                                # tqdm.write('记录创建结果：', result_rd)
                                if result_rd.get('code') == 0:
                                    success_count += 1
                                    tb_success_count += 1
                                else:
                                    tqdm.write(f'插入多维表格数据失败 {tb_name} 数据表 第{widget["page"]}页 {result_rd}')
                                tb_progress.set_postfix({
                                    '当前页': widget['page'],
                                    '剩余页数': totalPages - widget['page'],
                                    '总页数': totalPages,
                                    # '目标数量': f'{req_limit_rows}/{total_rows}',
                                    '目标数量': f'{req_limit_rows}',
                                    '累计抓取页数': success_count,
                                    '累计异常页数': fail_count,
                                })

                            # break
                            if widget['page'] < totalPages:
                                widget['page'] += 1
                            else:
                                break
                        else:
                            tqdm.write(f'【{tb_name}】 没有数据')
                            break
                    tb_progress.update(1)
                    tqdm.write(f'{widget_index + 1}【{tb_name}】数据获取完毕 {tb_success_count} / {totalPages}页 {total_rows}条') if total_rows else ''
                # time_all = sum([i[1] for i in pin.records])
                # tqdm.write(f'总耗时{time_all}秒')
                result = f'任务执行完毕。本次查询总数{all_total_count}条，共{all_total_pages}页数据，已录入{success_count}页数据，详情请前往飞书表格查看。【https://va81rsh3m92.feishu.cn/base/EKR7bRB24aZIKFszqO5cn3mCn9d】'
                result_data = {
                    'result': result,
                    'status': 10,
                    'page_count': all_total_pages,
                    'total': all_total_count,
                }
        except Exception as e:
            traceback.print_exc()
        tqdm.write(result_data['result'])
        return result_data


def convert_to_records(two_d_array):
    # 第一行作为表头
    headers = two_d_array[0]
    # 从第二行开始处理数据
    records = []
    for row in two_d_array[1:]:
        # 尝试将每个值转换为数字
        numeric_row = []
        for item in row:
            match = re.match(r'^-?\d+(,\d{3})*(\.\d+)?([eE][-+]?\d+)?$', item)
            if match:
                # 移除千位分隔符
                numeric_str = item.replace(',', '')
                # 根据是否有小数点决定转换为 int 或 float
                if '.' in numeric_str or 'e' in numeric_str.lower():
                    numeric_item = float(numeric_str)
                else:
                    numeric_item = int(numeric_str)
            else:
                # 如果不匹配，则保留原样
                numeric_item = item or ''
            numeric_row.append(numeric_item)
        # 创建字典并添加到记录列表
        records.append({"fields": dict(zip(headers, numeric_row))})

    return {"records": records}


def extract_table_data(tb_rs_data):
    """
    从表格数据对象中提取表头和行数据
    :param tb_rs_data: 表格数据对象，包含表头和行数据
    :return: 一个二维数组，第一行为表头，后续行为行数据
    """
    # 解构赋值，获取表格数据中的表头和行数据部分
    try:
        header, items = tb_rs_data['header'], tb_rs_data['items']

        # 调用extract_header函数提取表头数据
        table_headers = [item['text'] for item in header]
        # 调用extract_rows函数提取行数据
        table_rows = [[cell['formatText'] for cell in row] for row in items]
    except:
        traceback.print_exc()
        return []
    # 返回一个二维数组，第一行为表头，后续行为行数据
    return [table_headers] + table_rows


def create_table_request(tb_data, tb_name):
    """
    从二维数组创建一个用于创建数据表的请求体。
    :param tb_data: 二维数组，第一行为表头，其余行为数据
    :param tb_name: 数据表名称
    :return: 符合要求的JSON格式的请求体
    """
    # 获取表头
    headers = tb_data[0]
    data_row = tb_data[1]

    # 创建请求体
    json_d = {
        "table": {
            "name": tb_name.strip(),  # 去除首尾空格
            "fields": [
                {
                    "field_name": header.strip(),  # 去除首尾空格
                    "type": 1 if not is_numeric(row) else 2
                }
                for header, row in zip(headers, data_row)
            ]
        }
    }

    # 确保字段数量在允许范围内
    if len(json_d['table']['fields']) < 1 or len(json_d['table']['fields']) > 300:
        raise ValueError("字段数量必须在 1 到 300 之间。")

    return json_d


def is_numeric(s):
    """
    判断字符串s是否为数字类型。
    :param s: 字符串
    :return: 如果s是数字类型，则返回True；否则返回False
    """
    match = re.match(r'^-?\d+(,\d{3})*(\.\d+)?([eE][-+]?\d+)?$', s)
    if match:
        return True
    else:
        return False


def uuid(length=16):
    # 生成指定长度的十六进制随机字符串
    return ''.join(secrets.choice('0123456789abcdef') for _ in range(length))


if __name__ == '__main__':
    json_data = {"tid": "1727333185330",
                 "cookies": "tenantId=7b644ff5d9294d369800d883deac9d83; fr_id_appname=jiushuyun; fine_remember_login=-1; fine_auth_token=eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiI2ZTk5ZDc1MWU4YjA0OTVkYmY0MDBjM2U5NzEzMDIzZSIsInRlbmFudElkIjoiN2I2NDRmZjVkOTI5NGQzNjk4MDBkODgzZGVhYzlkODMiLCJpc3MiOiJmYW5ydWFuIiwiZGVzY3JpcHRpb24iOiJbNzUyOF1bNjIzN11kNTVUV3EzY2NWKDZlOTlkNzUxZThiMDQ5NWRiZjQwMGMzZTk3MTMwMjNlKSIsImV4cCI6MTcyNzkyMTM4MSwiaWF0IjoxNzI3MzE2NTgxLCJqdGkiOiJVK3dhdnB5cXJNbi9rT1BHMXJpVzhYN21tbkVGb0ZWS0dTT0tESlFxdVFma2VLVUwifQ.Mj1UiGERavzAlVuy7fNGlTeXyxn_n0q13nmETyiIKYQ",
                 "dashboardId": "8c98f15ca2f04109873de2cfd82da4d0"}
    skj = SKJ(json_data)
    skj.main()
