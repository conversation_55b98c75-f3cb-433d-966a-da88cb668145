import uuid
from collections import defaultdict


def generate_uuid():
    return str(uuid.uuid4())


def filter_result_data(data):
    if isinstance(data, dict):
        keys_to_remove = [key for key, value in data.items() if key in ['dimension', 'position', 'minResolution'] and all(v <= 0 for v in value.values())]
        for key in keys_to_remove:
            del data[key]
        for key, value in data.items():
            data[key] = filter_result_data(value)
    elif isinstance(data, list):
        for i in range(len(data)):
            data[i] = filter_result_data(data[i])
    return data


def decorators(func):
    def wrapper(*args, **kwargs):
        if len(args) > 1 and isinstance(args[1], dict):
            args = (args[0], defaultdict(lambda: '', args[1]), *args[2:])

        # 调用原始函数
        result_data = func(*args, **kwargs)
        # 过滤函数返回值
        filtered_result_data = filter_result_data(result_data)
        return filtered_result_data

    return wrapper


class GoodDiyRequestBodyBuilder:
    template_identifier_map = {
        "PageContainerComponent": "740ab4ea-9b02-408a-8894-b4ff30298c66",
        "PlacementContainerComponent": "740ab4ea-9b02-408a-8894-b4ff30298c66",
        "ImageInputComponent": "740ab4ea-9b02-408a-8894-b4ff30298c66",
    }

    def __init__(self):
        self.type_counter = {}

    @decorators
    def build_image_component(self, component):
        count = self.increment_counter('image')
        placement_container = {
            "identifier": generate_uuid(),
            "type": "PlacementContainerComponent",
            "templateIdentifier": self.template_identifier_map["PlacementContainerComponent"],
            "children": [
                {
                    "identifier": generate_uuid(),
                    "type": "ImageInputComponent",
                    "name": "",
                    "label": component["label"],
                    "instructions": component["instructions"],
                    "templateIdentifier": self.template_identifier_map["ImageInputComponent"],
                    "isRequired": component["isRequired"] == 'true',
                    'minResolution': {
                        "width": safe_int(component["minResolution.width"]),
                        "height": safe_int(component["minResolution.height"])
                    }
                }
            ],
            "dimension": {
                "width": safe_int(component["dimension.width"]),
                "height": safe_int(component["dimension.height"])
            },
            "position": {
                "x": safe_int(component["position.x"]),
                "y": safe_int(component["position.y"])
            },
            "isFreePlacement": True,
            "label": f"图片 {count}",
            "name": f"图片 {count}"
        }
        return placement_container

    def increment_counter(self, component_type):
        """递增特定组件类型的计数器并返回计数值"""
        if component_type not in self.type_counter:
            self.type_counter[component_type] = 0
        self.type_counter[component_type] += 1
        return self.type_counter[component_type]


def safe_int(value, default=0):
    try:
        return int(value)
    except ValueError:
        return default


if __name__ == '__main__':
    component = {
        'type': 'image',
        'label': 'Bild hochlade',
        'instructions': 'Wählen Sie Ihr hochauflösendes Bild zum Hochladen!',
        'isRequired': 'false',
        'dimension.width': '240',
        'dimension.height': '398',
        'position.x': '150',
        'position.y': '2',
        'minResolution.height': '80',
        'minResolution.width': '80',
    }
    t = GoodDiyRequestBodyBuilder()
    print(t.build_image_component(component))
