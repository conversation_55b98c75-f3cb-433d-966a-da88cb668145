import time
import requests
from utils_mrc.SpiderTools import IPPool
from utils_mrc.FeiShuAPI import fsmsg
from utils_mrc.MysqlHelper import MS, logging


class XiYouZhaoCi:
    def __init__(self):
        self.is_login = True
        self.default_authorization = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiN2RkZGYwMTEtMTUzMy00MzBlLWI1NjctNDY3YzM2ODkyYTgxIiwiSUQiOjAsIlBob25lTnVtIjoiIiwiTmlja05hbWUiOiIiLCJBdXRob3JpdHlJZCI6IiIsIkJ1ZmZlclRpbWUiOjAsImV4cCI6MTcyNTUzMTA1NywibmJmIjoxNzI0OTI0MjU3fQ.jEGBld7MI4AAoD4d6xbI1lk_cLTgxU7WeNJ9xip1ybs'
        self.headers = {
            **IPPool.headers,
            'authorization': self.default_authorization,
            'content-type': 'application/json',
            'origin': 'https://www.xiyouzhaoci.com',
            'referer': 'https://www.xiyouzhaoci.com/',
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.api_url = 'https://api.xiyouzhaoci.com/v2/asins/research/list'

    def _get_info_data(self):
        row = MS.get_dict_one('select * from rpa.rpa_info where source like "xyzc_cookies%"')
        if not row and self.api_url:
            fsmsg.send('亚马逊广告位数据采集', '西柚cookie查询失败！', '请先登录配置西柚cookie！')
            self.is_login = False
        authorization = row.get('remark') or self.default_authorization
        self.session.headers.update({'authorization': authorization})
        self.authorization = authorization
        self.source = row.get('source')
        self.expired = row.get('expired')
        return authorization

    def fetch_search_terms(self, asin: str, site: str) -> str:
        response = {}
        self._get_info_data()
        json_data = {
            'resource': {
                'country': site.upper(),
                'asin': asin,
            },
            'biz': {
                'asin': asin,
                'country': site.upper(),
                'page': 1,
                'pageSize': 10,
                'query': '',
                'orders': [
                    {'field': 'follow', 'order': 'desc'},
                ],
                'filters': [],
                'rangeFilters': [],
            },
        }

        try:
            for _ in range(3):
                proxies = IPPool.proxies
                try:
                    response = self.session.post(self.api_url, json=json_data, proxies=proxies)
                    break
                except Exception as e:
                    print(f"西柚发生错误: {e}")
                    pass
            data = response.json()
            data_list = data.get('list') or []
            total = data.get('total')
            if total is None:
                raise Exception(data.get('msg', '抓取异常'))
            word_list = [i.get('searchTerm', '') for i in data_list]
            self.is_login = True
            return ','.join(word_list) or '暂无'
        except Exception as e:
            print(f"发生错误: {e}")
            return str(e)

    def check_login_status(self):
        self._get_info_data()
        if self.expired == 0:
            self.is_login = True
        else:
            if self.is_login:
                fsmsg.send('西柚登录状态自检', '西柚登录状态未处理，请重新登录！')
            self.is_login = False
            return False
        asin = 'B0DDL9STRV'
        site = 'de'
        rs = self.fetch_search_terms(asin, site)
        if '登录状态过期，请重新登录' in rs and self.is_login:
            fsmsg.send('西柚登录状态自检', '西柚登录状态过期，请重新登录！', rs)
            self.is_login = False
            MS.update('update rpa.rpa_info set expired=1 where source=%s', self.source)
        return True

    def self_check(self, wait=120):
        while True:
            rs = self.check_login_status()
            logging('登录已失效！') if not rs else logging('登录状态正常！')
            time.sleep(wait)


xyzc = XiYouZhaoCi()
# 使用示例
if __name__ == "__main__":
    xyzc.self_check()
