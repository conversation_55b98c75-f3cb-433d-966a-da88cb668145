# 文件: work/自动化/领星抓取/FeeDiffProvider.py

from .LingXingSessionPage import LingXingSessionPage
from .DataFetcher import BaseDataProvider
from typing import Dict, List


class LingXingFeeDiffProvider(BaseDataProvider):
    """FBA费差异列表数据提供者"""

    def create_session(self):
        return LingXingSessionPage()

    def get_total(self, params: Dict) -> int:
        # 接口不返回总数，返回一次就足够，用 mids 控制分组
        return 1

    def fetch_page(self, user: str, page: int, page_size: int, params: Dict) -> List[Dict]:
        self.session.user = user
        # 只支持一次请求，不分页，因此 offset 始终为 0
        params['offset'] = 0
        params['length'] = page_size
        return self.session.api_fee_diff_list(params)

    def process_item(self, item: Dict) -> Dict:
        item['unique_id'] = item.get('order_id', '') + item.get('sku', '')
        return item

    @property
    def table_name(self) -> str:
        return 'rpa.data_lingxing_fba_feediff'
