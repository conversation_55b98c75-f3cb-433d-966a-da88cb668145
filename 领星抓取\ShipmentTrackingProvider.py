# 文件: work/自动化/领星抓取/ShipmentTrackingProvider.py

from .LingXingSessionPage import LingXingSessionPage
from .DataFetcher import BaseDataProvider
from typing import Dict, List
import time


class LingXingShipmentTrackingProvider(BaseDataProvider):
    """货件签收差异数据提供者"""

    def create_session(self):
        return LingXingSessionPage()

    def get_total(self, params: Dict) -> int:
        """获取总数据量 - 这里需要先获取店铺列表，然后计算总页数"""
        try:
            # 先获取店铺列表
            shop_list = self.session.api_shipment_claim_summary(params)
            if not shop_list:
                return 0
            
            # 计算每个店铺的数据量，这里简化处理，假设每个店铺最多有200条数据
            # 实际应该调用每个店铺的详情接口获取准确数量
            total_shops = len(shop_list)
            # 假设平均每个店铺有50条数据，分页大小200，所以大部分店铺只需要1页
            estimated_pages = max(1, total_shops)  # 至少1页
            return estimated_pages
        except Exception as e:
            print(f"获取总数据量失败: {e}")
            return 1  # 返回1确保至少执行一次

    def fetch_page(self, user: str, page: int, page_size: int, params: Dict) -> List[Dict]:
        """获取分页数据 - 实际上是获取所有店铺的详细数据"""
        self.session.user = user
        all_data = []

        try:
            # 第一步：获取店铺列表
            print(f"[{user}] 开始获取店铺汇总信息...")
            summary_params = {
                'sid_list': [],
                'mid_list': [],
                'offset': 0,
                'length': 200,
                'req_time_sequence': '/api/tool/shipment_claim/getSummaryBody$$5'
            }
            shop_list = self.session.api_shipment_claim_summary(summary_params)

            if not shop_list:
                print(f"[{user}] 未获取到店铺信息")
                return []

            print(f"[{user}] 获取到 {len(shop_list)} 个店铺，开始逐个采集详细数据...")

            # 第二步：循环获取每个店铺的详细数据
            for idx, shop in enumerate(shop_list, 1):
                sid = shop.get('sid')
                shop_name = shop.get('sellerName', 'Unknown')
                country = shop.get('countryName', 'Unknown')

                if not sid:
                    print(f"[{user}] 店铺 {idx}/{len(shop_list)} ({shop_name}-{country}) 缺少SID，跳过")
                    continue

                print(f"[{user}] 正在采集店铺 {idx}/{len(shop_list)}: {shop_name}-{country} (SID: {sid})")

                shop_total_data = 0
                # 获取该店铺的详细数据，支持分页
                offset = 0
                page_num = 1
                while True:
                    detail_params = {
                        'sid': sid,
                        'sort_field': '',
                        'sort_type': '',
                        'offset': offset,
                        'length': 200,  # 每次最多200条
                        'status': 0,
                        'search_value': '',
                        'search_field': 'msku',
                        'req_time_sequence': '/api/tool/shipment_claim/list$$12'
                    }

                    detail_data = self.session.api_shipment_claim_list(detail_params)
                    if not detail_data:
                        break

                    print(f"[{user}]   └─ 第{page_num}页: 获取到 {len(detail_data)} 条数据")

                    # 为每条详细数据添加店铺信息
                    for item in detail_data:
                        item['shop_info'] = {
                            'sid': shop.get('sid'),
                            'mid': shop.get('mid'),
                            'sellerName': shop.get('sellerName'),
                            'countryName': shop.get('countryName')
                        }

                    all_data.extend(detail_data)
                    shop_total_data += len(detail_data)

                    # 如果返回的数据少于200条，说明已经是最后一页
                    if len(detail_data) < 200:
                        break

                    offset += 200
                    page_num += 1
                    time.sleep(0.1)  # 避免请求过快

                print(f"[{user}]   └─ 店铺 {shop_name}-{country} 共采集 {shop_total_data} 条数据")
                time.sleep(0.2)  # 店铺间隔稍长一些

            print(f"[{user}] 所有店铺采集完成，总计 {len(all_data)} 条数据")
            return all_data
            
        except Exception as e:
            print(f"获取货件签收差异数据失败: {e}")
            return []

    def process_item(self, item: Dict) -> Dict:
        """处理单条数据，进行字段映射"""
        shop_info = item.get('shop_info', {})
        
        # 字段映射：API字段 → 数据库字段
        processed_item = {
            'msku': item.get('msku', ''),
            'fnsku': item.get('fnsku', ''),
            'asin': item.get('asin', ''),
            'shipment_id': item.get('shipmentId', ''),
            'shipment_name': item.get('shipmentName', ''),
            'tracking_number': item.get('shipmentReferenceId', ''),  # 使用shipmentReferenceId作为追踪编号
            'store': shop_info.get('sellerName', ''),
            'country': shop_info.get('countryName', ''),
            'declared_quantity': item.get('shipmentQuantityShippedLocal', 0),  # 申报量
            'shipped_quantity': item.get('shipmentQuantityShipped', 0),  # 已发货量
            'received_quantity': item.get('shipmentQuantityReceived', 0),  # 签收量
            'pending_diagnosis_quantity': item.get('estimateDiagnosticNum', 0),  # 预计可诊断数量
            'pending_diagnosis_amount': float(item.get('estimateDiagnosticAmount', 0)),  # 预计可诊断金额
            'remaining_diagnosis_days': item.get('estimateRemainDaysForDiagnostic', 0),  # 剩余可诊断天数
            'status': item.get('statusCnName', ''),  # 状态
            'remark': item.get('remark', ''),  # 备注
            # 添加唯一标识
            'unique_id': f"{item.get('shipmentId', '')}_{item.get('msku', '')}_{shop_info.get('sid', '')}"
        }
        
        return processed_item

    @property
    def table_name(self) -> str:
        return 'rpa.data_lingxing_shipment_tracking'
