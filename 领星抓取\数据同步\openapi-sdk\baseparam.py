from openapi import OpenApiBase
import time


class GetToken:
    def __init__(self):
        self.host = "https://openapi.yxyglobal.com"
        self.app_id = "ak_bxQLTwBumJkv0"
        self.app_secret = "x96BnSw8I1gLz7qyLdbB8w=="
        self.op_api = OpenApiBase(self.host, self.app_id, self.app_secret)

        # Store the token and its expiration time
        self.access_token = None
        self.access_token_expiration = 0
        self.refresh_token = None

    async def generate_access_token(self):
        resp = await self.op_api.generate_access_token()
        # print("数据:", resp.dict())
        assert resp.access_token
        self.access_token = resp.access_token
        self.refresh_token = resp.refresh_token
        self.access_token_expiration = time.time() + 7100  # Set expiration time

        return resp.access_token
