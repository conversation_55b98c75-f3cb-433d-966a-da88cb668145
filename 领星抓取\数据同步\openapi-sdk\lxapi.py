# -*- coding:UTF-8 -*-
# @FileName  :product.py
# @Time      :2024/7/15 13:57
# <AUTHOR>
from utils_mrc.MysqlHelper import *
import baseparam
import asyncio
import json


class LXAPI(baseparam.GetToken):
    def __init__(self):
        baseparam.GetToken.__init__(self)  # Initialize GetToken class
        self.data_list = []

    async def get_product_list(self):
        options = {
            'offset': 0,  # 分页偏移量，默认0
            'length': 10,  # 分页长度，默认1000，上限1000
            # 'update_time_start': 1688530598,  # 更新时间-开始时间【时间戳，单位：秒，左闭右开】
            # 'update_time_end': **********,  # 更新时间-结束时间【时间戳，单位：秒，左闭右开】
            # 'create_time_start': **********,  # 创建时间-开始时间【时间戳，单位：秒，左闭右开】
            # 'create_time_end': **********,  # 创建时间-结束时间【时间戳，单位：秒，左闭右开】
            # 'sku_list': ["AAAA", "BBBBB"]  # 本地产品sku
        }

        resp = await self.op_api.request(
                self.access_token,
                "/erp/sc/routing/data/local_inventory/productList",
                "POST",
                req_body=options
        )
        self.data_list = resp.dict()["data"]
        # print(resp.dict())
        # print(self.sid_list)
        return resp.dict()

    async def get_product_detail(self):
        options = {
            'id': 10004,
            'sku': 'Kgz-szheihui',
        }

        resp = await self.op_api.request(
                self.access_token,
                "/erp/sc/routing/data/local_inventory/productInfo",
                "POST",
                req_body=options
        )
        self.data_list = resp.dict()["data"]
        return resp.dict()

    async def get_product_detail_batch(self):
        options = {
            # 'productIds': [10004],
            'skus': ['Kgz-kpsznhui', 'Kgz-hbpszhei', 'T-FGMRYWST-HXF0007', 'T-BKMTZYSK-LZF0049FF', 'Pfcp-hei', 'gmyz-sxa03s'],
        }

        resp = await self.op_api.request(
                self.access_token,
                "/erp/sc/routing/data/local_inventory/batchGetProductInfo",
                "POST",
                req_body=options
        )
        self.data_list = resp.dict()["data"]
        return resp.dict()

    async def get_seller(self):
        resp = await self.op_api.request(
                self.access_token,
                "/erp/sc/data/seller/lists",
                "GET",
        )
        self.data_list = resp.dict()["data"]
        return resp.dict()


def parse_data_goods(data):
    data_list = []

    for item in data:
        goods_dict = {
            'id': item.get('id'),
            'goods_sku': item.get('sku'),
            'goods_name': item.get('product_name'),
            'goods_category_lastid': item.get('category_id'),
            'status': item.get('status', 1),  # 设置默认值为1
            'develop_user_id': item.get('product_developer_uid'),
            'develop_user': item.get('product_developer'),
            'create_user_id': item.get('product_creator_uid') or item.get('product_developer_uid'),  # 使用开发者ID作为默认值
            'goods_stock': float(item.get('cg_price')),
            'goods_attribute': str(item.get('special_attr')),
        }
        data_list.append(goods_dict)
    return data_list


def parse_data_goods_attach(data):
    # 定义一个空列表来保存转换后的数据
    data_list = []

    for item in data:
        goods_attach_dict = {
            'goods_id': item.get('id'),
            'length': item.get('cg_product_length'),
            'width': item.get('cg_product_width'),
            'height': item.get('cg_product_height'),
            'weight_net': float(item.get('cg_product_net_weight')),
            'weight': float(item.get('cg_product_gross_weight')),
            'box_length': item.get('cg_box_length'),
            'box_width': item.get('cg_box_width'),
            'box_height': item.get('cg_box_height'),
            'box_weight_net': float(item.get('cg_box_weight')),
            'box_num': int(item.get('cg_box_pcs')),
        }
        # 添加到数据列表中
        data_list.append(goods_attach_dict)

    return data_list


def parse_data_goods_bom(data):
    # 定义一个空列表来保存转换后的数据
    goods_bom_data_list = []

    for item in data:
        # 检查group_list字段是否存在
        group_list = item.get('group_list')
        if group_list:
            # 遍历group_list中的每一个对象
            for sub_item in group_list:
                # 创建一个字典来保存goods_bom表的字段
                goods_bom_dict = {
                    'goods_id': item.get('id'),
                    'goods_sku': item.get('sku'),  # 主商品的SKU
                    'bom_goods_sku': sub_item.get('sku'),  # 子商品的SKU
                    'bom_goods_num': int(sub_item.get('quantity'))  # 商品比例数
                }

                # 添加到数据列表中
                goods_bom_data_list.append(goods_bom_dict)

    return goods_bom_data_list


def parse_data_goods_declare(data):
    # 定义一个空列表来保存转换后的数据
    goods_declare_data_list = []

    for item in data:
        # 创建一个字典来保存goods_declare表的字段
        goods_declare_dict = {
            'goods_id': item.get('id'),
            'goods_declare': item.get('bg_customs_export_name'),
            'goods_inland_code': item.get('bg_export_hs_code'),
            'goods_declare_en': item.get('bg_customs_import_name'),
            'goods_declare_usd': float(item.get('bg_customs_import_price')),
            'goods_declare_company': item.get('declaration', {}).get('customs_declaration_unit'),
        }

        # 添加到数据列表中
        goods_declare_data_list.append(goods_declare_dict)

    return goods_declare_data_list


def parse_data_goods_detail(data):
    # 定义一个空列表来保存转换后的数据
    goods_detail_data_list = []

    for item in data:
        # 创建一个字典来保存goods_detail表的字段
        goods_detail_dict = {
            'goods_id': item.get('id'),
            'detail': item.get('description'),
            'lang': 'cn',
        }

        # 添加到数据列表中
        goods_detail_data_list.append(goods_detail_dict)

    return goods_detail_data_list


def parse_data_goods_factory(data):
    # 定义一个空列表来保存转换后的数据
    goods_factory_data_list = []

    for item in data:
        # 获取supplier_quote字段，如果不存在则使用空字典
        supplier_quote = item.get('supplier_quote', {})

        # 获取quotes字段，如果不存在则使用空列表
        quotes = supplier_quote.get('quotes', [])

        # 遍历quotes中的每一个对象
        for quote in quotes:
            # 获取step_prices字段，如果不存在则使用空列表
            step_prices = quote.get('step_prices', [])

            # 遍历step_prices中的每一个对象
            for price_info in step_prices:
                # 将price_info转换为字符串
                price_info_str = json.dumps(price_info, separators=(',', ':'))

                # 创建一个字典来保存goods_factory表的字段
                goods_factory_dict = {
                    'goods_id': item.get('id'),
                    'price_json': price_info_str,
                    'min_quantity': int(price_info.get('moq', 1)),
                }

                # 添加到数据列表中
                goods_factory_data_list.append(goods_factory_dict)

    return goods_factory_data_list


def parse_data_goods_image(data):
    # 定义一个空列表来保存转换后的数据
    goods_image_data_list = []

    for item in data:
        # 获取picture_list字段，如果不存在则使用空列表
        picture_list = item.get('picture_list', [])

        # 遍历picture_list中的每一个图片信息对象
        for pic_info in picture_list:
            # 创建一个字典来保存goods_image表的字段
            goods_image_dict = {
                'goods_id': item.get('id'),
                'url': pic_info.get('pic_url'),
                'type': 1 - int(pic_info.get('is_primary', 0)),  # 取反操作
            }

            # 添加到数据列表中
            goods_image_data_list.append(goods_image_dict)

    return goods_image_data_list


def parse_data_dev_goods_sku(data):
    # 定义一个空列表来保存转换后的数据
    dev_goods_sku_data_list = []

    for item in data:
        # 创建一个字典来保存dev_goods_sku表的字段
        dev_goods_sku_dict = {
            'goods_id': item.get('id'),
            'goods_unit': item.get('unit'),
        }

        # 添加到数据列表中
        dev_goods_sku_data_list.append(dev_goods_sku_dict)

    return dev_goods_sku_data_list


def parse_data_dev_goods_sku_stock(data):
    # 定义一个空列表来保存转换后的数据
    dev_goods_sku_stock_data_list = []

    for item in data:
        # 创建一个字典来保存dev_goods_sku_stock表的字段
        dev_goods_sku_stock_dict = {
            'goods_id': item.get('id'),
            'stock_user_id': item.get('cg_opt_uid'),
            'stock_user': item.get('cg_opt_username'),
            'remarks': item.get('purchase_remark'),
            'delivery_time': item.get('cg_delivery'),
        }

        # 处理supplier_quote字段
        supplier_quote = item.get('supplier_quote')
        if supplier_quote:
            # 默认使用第一个供应商报价信息
            first_quote = supplier_quote[0] if supplier_quote else {}

            dev_goods_sku_stock_dict.update({
                'goods_id': item.get('id'),
                'factory': first_quote.get('erp_supplier_id') or first_quote.get('supplier_id'),
                'url': first_quote.get('supplier_product_url', []),
                'remarks': first_quote.get('quote_remark'),
                'currency': first_quote.get('quotes', [{}])[0].get('currency'),
                'tax': first_quote.get('quotes', [{}])[0].get('tax_rate', '0'),
            })

        # 添加到数据列表中
        dev_goods_sku_stock_data_list.append(dev_goods_sku_stock_dict)

    return dev_goods_sku_stock_data_list


sql = """
        INSERT INTO gms.`goods` (
            `id`, `goods_name`, `goods_sku`, `goods_image`, `goods_sku_alias`,
            `goods_brand`, `goods_category`, `develop_user`, `develop_user_id`,
            `goods_cost`, `status_sales`, `goods_attr`, `goods_attribute`,
            `is_mixin`, `create_user`, `create_user_id`, `goods_stock`, `remarks`
        ) VALUES (
            %(id)s, %(goods_name)s, %(goods_sku)s, %(goods_image)s, %(goods_sku_alias)s,
            %(goods_brand)s, %(goods_category)s, %(develop_user)s, %(develop_user_id)s,
            %(goods_cost)s, %(status_sales)s, %(goods_attr)s, %(goods_attribute)s,
            %(is_mixin)s, %(create_user)s, %(create_user_id)s, %(goods_stock)s, %(remarks)s
        )
        """


async def main_product():
    product = LXAPI()
    await product.generate_access_token()
    # result = await product.get_product_list()
    # result = await product.get_product_detail()
    result = await product.get_product_detail_batch()
    data_list = parse_data_goods(result['data'])

    # print(result)
    # print("获取列表", product.data_list)
    print(f'提取后列表', data_list)


async def main_seller():
    lxapi = LXAPI()
    await lxapi.generate_access_token()
    result = await lxapi.get_seller()

    print(result)
    # print("获取列表", lxapi.data_list)


if __name__ == "__main__":
    loop = asyncio.get_event_loop()
    loop.run_until_complete(main_seller())
